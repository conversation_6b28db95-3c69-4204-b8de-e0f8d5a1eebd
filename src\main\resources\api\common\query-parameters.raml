#%RAML 1.0 Library
usage: Query Parameters to be used by the ERP API
types:
  counterpartyId:
    displayName: Counterparty ID
    description: Artemis CounterpartyID
    example: 123456
    type: integer
    format: int32
  confirmId:
    displayName: Confirm ID
    description: Artemis ConfirmID
    example: 123456
    type: integer
    format: int32
  email:
    displayName: Email
    description: Email Address
    example: <EMAIL>
    type: string
  country:
    displayName: Country
    description: Country Name or Country Code
    example: United States
    type: string
  countryCode:
    displayName: Country Code
    description: country code
    example: US
    type: string
  leadSource:
    displayName: Lead Source
    description: Salesforce Lead Source
    example: Marketplace
    type: string
  recordTypeId:
    displayName: RecordTypeId
    description: Salesforce RecordTypeId
    example: 012Jw000002JZXBIA4
    type: string
  recordTypeName:
    displayName: RecordType Name
    description: Salesforce RecordType.Name
  pricebookId:
    displayName: Pricebook2Id
    description: Salesforce Pricebook2Id
  pricebookName:
    displayName: Pricebook2 Name
    description: Salesforce Pricebook2.Name
  climatePortalAccountId:
    displayName: Climate Action Portal Account ID
    description: Salesforce Climate Action Portal Account ID maps to Climate Portal Company ID
    example: 240
    type: number
  climatePortalContactId:
    displayName: Climate Action Portal Contact ID
    description: Saleforce Climate Action Portal Contact ID maps to Climate Portal Customer ID
    example: 428
    type: number
  database:
    displayName: Database
    description: Artemis API Database
    example: Artemis
    type: string
  schema:
    displayName: Schema
    description: Artemis API Schema
    example: port
    type: string
  objectType:
    displayName: Object Type
    description: Artemis API Object Type
    example: StoredProcedure
    type: string
  objectName:
    displayName: Object Name
    description: Artemis API Object Name
    example: tdp_Companies
    type: string
  filters:
    displayName: Filter
    description: Artemis API Filter
    example: 'where%20CommodityID%20in%20%281%2C%202%2C%203%2C%206%29'
    type: string
  parameters:
    displayName: Parameters
    description: Artemis API Parameters
    example: '@ConfirmID=11704'
    type: string
  pageSize:
    displayName: Page Size
    description: Page Size of Response
    default: 100
    example: 100
    type: number
  pageNumber:
    displayName: Page Number
    description: Page Number of Response
    default: 1
    example: 1
    type: number