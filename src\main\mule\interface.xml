<?xml version="1.0" encoding="UTF-8"?>
<mule xmlns:os="http://www.mulesoft.org/schema/mule/os" xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:apikit="http://www.mulesoft.org/schema/mule/mule-apikit" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core" xmlns:http="http://www.mulesoft.org/schema/mule/http" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd http://www.mulesoft.org/schema/mule/mule-apikit http://www.mulesoft.org/schema/mule/mule-apikit/current/mule-apikit.xsd http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd 
http://www.mulesoft.org/schema/mule/os http://www.mulesoft.org/schema/mule/os/current/mule-os.xsd">
    <apikit:config name="router" api="resource::4671abf7-f2d5-45a1-9ee4-7b645bf0fa49:erp-api:1.0.46:raml:zip:erp-api.raml" outboundHeadersMapName="outboundHeaders" httpStatusVarName="httpStatus" />
      <flow name="main">
     <http:listener path="/api/${api.version}/*" config-ref="https_listener">
            <http:response statusCode="#[(vars.httpStatus default Mule::p('apikit.success.ok.code')) as Number]" reasonPhrase="#[(vars.httpReasonPhrase default Mule::p('apikit.success.ok.description')) as String]">
                <http:body ><![CDATA[#[payload default {}]]]></http:body>
				<http:headers><![CDATA[#[vars.outboundHeaders default {}]]]></http:headers>
            </http:response>
            <http:error-response statusCode="#[(vars.httpStatus default Mule::p('apikit.errors.default.code')) as Number]" reasonPhrase="#[(vars.httpReasonPhrase default Mule::p('apikit.errors.default.description')) as String]">
                <http:body><![CDATA[#[payload default {}]]]></http:body>
                <http:headers><![CDATA[#[vars.outboundHeaders default {}]]]></http:headers>
            </http:error-response>
        </http:listener>
		<ee:transform doc:name="Request" doc:id="d8a14278-e043-4763-a417-e39f3f3b6473">
			<ee:message>
			</ee:message>
			<ee:variables>
				<ee:set-variable variableName="request"><![CDATA[%dw 2.0
output application/java
---
{
	method: attributes.method,
	path: attributes.maskedRequestPath,
	correlationID: correlationId
} ++ if ( !isEmpty(attributes.uriParams) ) {
	uriParameters: attributes.uriParams
} else {
} ++ if ( !isEmpty(attributes.queryParams) ) {
	queryParameters: attributes.queryParams
} else {
}]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<apikit:router config-ref="router" />
    
      </flow>

     <flow name="get:\artemis\document:router">
   <logger level="INFO" doc:name="LOG INFO: Inbound Request" doc:id="ef6e2638-6d45-4e09-b1cb-3d3e84fcaa76" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "INBOUND REQUEST",&#10;	"Request": vars.request&#10;}]' />
		<flow-ref doc:name="Artemis API Document" doc:id="33ef2c55-bc04-47ea-bde9-e294a21aae9f" name="artemis_document_get" />
		<ee:transform doc:name="Outbound Response" doc:id="656d16f7-f723-4f64-bdee-50c4ded9a036">
			<ee:message>
				<ee:set-payload><![CDATA[payload]]></ee:set-payload>
			</ee:message>
			<ee:variables >
				<ee:set-variable variableName="httpStatus" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.code') as Number]]></ee:set-variable>
				<ee:set-variable variableName="httpReasonPhrase" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.description')]]></ee:set-variable>
			</ee:variables>
	
		</ee:transform>
		<logger level="INFO" doc:name="LOG INFO:  Outbound Response" doc:id="18ba243c-8dbc-433f-a344-31199f41f4d8" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "OUTBOUND RESPONSE"&#10;	&#10;}]' />
		

    </flow>
    <flow name="get:\artemis\stored-procedure:router">
        <logger level="INFO" doc:name="LOG INFO: Inbound Request" doc:id="cdab4e35-844c-410a-96e9-606622c2f557" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "INBOUND REQUEST",&#10;	"Request": vars.request&#10;}]' />
		<flow-ref doc:name="Artemis API Stored Procedure" doc:id="c9796f87-d2fd-441a-b906-4d4f82859017" name="artemis_uda_stored-procedure" />
		<ee:transform doc:name="Outbound Response" doc:id="415e3bfe-e792-4200-a1ab-49f2a420e2f4">
			<ee:message>
				<ee:set-payload><![CDATA[%dw 2.0
output application/json
---
{
	statusCode: Mule::p('apikit.success.ok.code') as Number,
	reasonPhrase: Mule::p('apikit.success.ok.description'),
	response: payload
}]]></ee:set-payload>
			</ee:message>
			<ee:variables>
				<ee:set-variable variableName="httpStatus" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.code') as Number]]></ee:set-variable>
				<ee:set-variable variableName="httpReasonPhrase" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.description')]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<logger level="INFO" doc:name="LOG INFO:  Outbound Response" doc:id="c947a27a-73d5-4d8e-a310-71cbe25b4be4" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "OUTBOUND RESPONSE"&#10;	&#10;}]' />
    </flow>
    <flow name="get:\artemis\table:router">
    <logger level="INFO" doc:name="LOG INFO: Inbound Request" doc:id="e8cdda6e-1567-4078-ad40-8fd03a9f12aa" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "INBOUND REQUEST",&#10;	"Request": vars.request&#10;}]' />
		<flow-ref doc:name="Artemis API Table" doc:id="eb090969-6ebf-4d79-af0b-048e57f6d397" name="artemis_uda_table"/>
		<ee:transform doc:name="Outbound Response" doc:id="655f486d-7342-49d2-98be-41c12dacc766" >
			<ee:message >
				<ee:set-payload ><![CDATA[%dw 2.0
output application/json
---
{
	statusCode: Mule::p('apikit.success.ok.code') as Number,
	reasonPhrase: Mule::p('apikit.success.ok.description'),
	response: payload
}]]></ee:set-payload>
			</ee:message>
			<ee:variables>
				<ee:set-variable variableName="httpStatus" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.code') as Number]]></ee:set-variable>
				<ee:set-variable variableName="httpReasonPhrase" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.description')]]></ee:set-variable>
			</ee:variables>
	
		</ee:transform>
		<logger level="INFO" doc:name="LOG INFO:  Outbound Response" doc:id="897cc4dd-7858-4383-93e3-c61412878ed7" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "OUTBOUND RESPONSE"&#10;	&#10;}]' />
    </flow>
    <flow name="post:\artemis\insert:application\json:router">
    <logger level="INFO" doc:name="LOG INFO: Inbound Request" doc:id="0b333c13-e609-425a-b877-8447d4ac59b4" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "INBOUND REQUEST",&#10;	"Request": vars.request&#10;}]' />
		<flow-ref doc:name="Artemis API Insert" doc:id="0b753922-c2c9-4ecf-8409-ff92f2f6709a" name="artemis_uda_insert" />
		<ee:transform doc:name="Outbound Response" doc:id="f9ae224e-2784-4739-ad2d-2d73a660f45c" >
			<ee:message >
				<ee:set-payload ><![CDATA[%dw 2.0
output application/json
---
{
	message: payload.message,
	details: payload.details
}]]></ee:set-payload>
			</ee:message>
			<ee:variables >
				<ee:set-variable variableName="httpStatus" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.created.code') as Number]]></ee:set-variable>
				<ee:set-variable variableName="httpReasonPhrase" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.created.description')]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<logger level="INFO" doc:name="LOG INFO:  Outbound Response" doc:id="0c615ec4-5cc6-4dd3-8773-77b8269a6edd" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "OUTBOUND RESPONSE",&#10;	"Response": payload&#10;}]' />
	</flow>
	<flow name="get:\salesforce\account\service:router">
		<ee:transform doc:name="Outbound Response" doc:id="73d971a1-a3af-46dc-84d2-40eadbf31126" >
			<ee:message >
				<ee:set-payload ><![CDATA[%dw 2.0
output application/json
---
{
	statusCode: Mule::p('apikit.errors.notImplemented.code') as Number,
	reasonPhrase: Mule::p('apikit.errors.notImplemented.description'),
	response: {
		message: "DEPRECATED",
		details: ""
	}
}]]></ee:set-payload>
			</ee:message>
			<ee:variables >
				<ee:set-variable variableName="httpStatus" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.code') as Number]]></ee:set-variable>
				<ee:set-variable variableName="httpReasonPhrase" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.description')]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
    </flow>
    <flow name="post:\salesforce\file:router">
		<ee:transform doc:name="Outbound Response" doc:id="3d99bf67-b0c3-4dd7-9908-dd586311e31a" >
			<ee:message >
				<ee:set-payload ><![CDATA[%dw 2.0
output application/json
---
{
	statusCode: Mule::p('apikit.errors.notImplemented.code') as Number,
	reasonPhrase: Mule::p('apikit.errors.notImplemented.description'),
	response: {
		message: "DEPRECATED",
		details: ""
	}
}]]></ee:set-payload>
			</ee:message>
			<ee:variables >
				<ee:set-variable variableName="httpStatus" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.code') as Number]]></ee:set-variable>
				<ee:set-variable variableName="httpReasonPhrase" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.description')]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
    </flow>
</mule>
