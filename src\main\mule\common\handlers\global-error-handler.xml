<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:cloudhub="http://www.mulesoft.org/schema/mule/cloudhub" xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
    xmlns:tls="http://www.mulesoft.org/schema/mule/tls" xmlns:http="http://www.mulesoft.org/schema/mule/http"
    xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
http://www.mulesoft.org/schema/mule/tls http://www.mulesoft.org/schema/mule/tls/current/mule-tls.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd
http://www.mulesoft.org/schema/mule/cloudhub http://www.mulesoft.org/schema/mule/cloudhub/current/mule-cloudhub.xsd">

    <error-handler name="global-error-handler">
		<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate" doc:id="e8c5e3b5-4c80-4927-aee3-aee23a16a84c" type="HTTP:FORBIDDEN">
			<logger level="ERROR" doc:name="LOG ERROR: Error" doc:id="11367b02-e085-477e-b59d-79f7d8d28a2c" message="#[output application/json&#10;---&#10;{&#10;	Message: error.description,&#10;	CorrelationId: vars.request.correlationID,&#10;	Errors: error&#10;}]" />
			<ee:transform doc:name="Outbound Error Response" doc:id="f0dc1e8f-f6e6-4479-9b4f-2d126f030b3c" >
				<ee:message >
					<ee:set-payload ><![CDATA[%dw 2.0
output application/json
---
{
	statusCode: Mule::p('apikit.errors.forbidden.code') as Number,
	reasonPhrase: Mule::p('apikit.errors.forbidden.description'),
	response: {
		message: error.description,
		details: error
	}
}]]></ee:set-payload>
				</ee:message>
				<ee:variables >
					<ee:set-variable variableName="httpStatus" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.code') as Number]]></ee:set-variable>
					<ee:set-variable variableName="httpReasonPhrase" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.description')]]></ee:set-variable>
				</ee:variables>
			</ee:transform>
		</on-error-propagate>
		<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate" doc:id="e0820b46-dd31-43df-9a72-397897d7ab3c" type="APIKIT:BAD_REQUEST" >
			<logger level="ERROR" doc:name="LOG ERROR: Error" doc:id="1bf6883c-c786-4f95-8665-b550049bd9cb" message="#[output application/json&#10;---&#10;{&#10;	Message: error.description,&#10;	CorrelationId: vars.request.correlationID,&#10;	Errors: error&#10;}]" />
			<ee:transform doc:name="Outbound Error Response" doc:id="dffe93f9-8365-4544-afb2-be5a5bee9507" >
				<ee:message >
					<ee:set-payload ><![CDATA[%dw 2.0
output application/json
---
{
	statusCode: Mule::p('apikit.errors.badRequest.code') as Number,
	reasonPhrase: Mule::p('apikit.errors.badRequest.description'),
	response: {
		message: error.description,
		details: error
	}
}]]></ee:set-payload>
				</ee:message>
				<ee:variables >
					<ee:set-variable variableName="httpStatus" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.code') as Number]]></ee:set-variable>
					<ee:set-variable variableName="httpReasonPhrase" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.description')]]></ee:set-variable>
				</ee:variables>
			</ee:transform>
		</on-error-propagate>
		<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate" doc:id="5577fdd1-9256-4a9f-830b-4dc143522715" type="APIKIT:NOT_FOUND" >
			<logger level="ERROR" doc:name="LOG ERROR: Error" doc:id="5658c180-485a-4b10-a7a0-0fd78541d799" message="#[output application/json&#10;---&#10;{&#10;	Message: error.description,&#10;	CorrelationId: vars.request.correlationID,&#10;	Errors: error&#10;}]" />
			<ee:transform doc:name="Outbound Error Response" doc:id="376f3807-d76e-4de1-a927-e688673080df" >
				<ee:message >
					<ee:set-payload ><![CDATA[%dw 2.0
output application/json
---
{
	statusCode: Mule::p('apikit.errors.notFound.code') as Number,
	reasonPhrase: Mule::p('apikit.errors.notFound.description'),
	response: {
		message: error.description,
		details: error
	}
}]]></ee:set-payload>
				</ee:message>
				<ee:variables >
					<ee:set-variable variableName="httpStatus" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.code') as Number]]></ee:set-variable>
					<ee:set-variable variableName="httpReasonPhrase" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.description')]]></ee:set-variable>
				</ee:variables>
			</ee:transform>
		</on-error-propagate>
		<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate" doc:id="7569a93f-972a-4415-84db-8f77ff5a9d47" type="APIKIT:METHOD_NOT_ALLOWED" >
			<logger level="ERROR" doc:name="LOG ERROR: Error" doc:id="e8fd2528-ac29-4ba0-8342-d52e7f99c55b" message="#[output application/json&#10;---&#10;{&#10;	Message: error.description,&#10;	CorrelationId: vars.request.correlationID,&#10;	Errors: error&#10;}]" />
			<ee:transform doc:name="Outbound Error Response" doc:id="cc1a6326-9fcd-4f66-af67-c7e20d53a9c0" >
				<ee:message >
					<ee:set-payload ><![CDATA[%dw 2.0
output application/json
---
{
	statusCode: Mule::p('apikit.errors.methodNotAllowed.code') as Number,
	reasonPhrase: Mule::p('apikit.errors.methodNotAllowed.description'),
	response: {
		message: error.description,
		details: error
	}
}]]></ee:set-payload>
				</ee:message>
				<ee:variables >
					<ee:set-variable variableName="httpStatus" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.code') as Number]]></ee:set-variable>
					<ee:set-variable variableName="httpReasonPhrase" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.description')]]></ee:set-variable>
				</ee:variables>
			</ee:transform>
		</on-error-propagate>
		<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate" doc:id="829cac02-82b0-49e0-9d0b-4b52d3268c4a" type="APIKIT:NOT_ACCEPTABLE" >
			<logger level="ERROR" doc:name="LOG ERROR: Error" doc:id="cc8db46a-744e-4b9b-accc-27e5fd2660f7" message="#[output application/json&#10;---&#10;{&#10;	Message: error.description,&#10;	CorrelationId: vars.request.correlationID,&#10;	Errors: error&#10;}]" />
			<ee:transform doc:name="Outbound Error Response" doc:id="6f686f67-1714-4fd8-b0a9-1e019ede3877" >
				<ee:message >
					<ee:set-payload ><![CDATA[%dw 2.0
output application/json
---
{
	statusCode: Mule::p('apikit.errors.notAcceptable.code') as Number,
	reasonPhrase: Mule::p('apikit.errors.notAcceptable.description'),
	response: {
		message: error.description,
		details: error
	}
}]]></ee:set-payload>
				</ee:message>
				<ee:variables >
					<ee:set-variable variableName="httpStatus" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.code') as Number]]></ee:set-variable>
					<ee:set-variable variableName="httpReasonPhrase" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.description')]]></ee:set-variable>
				</ee:variables>
			</ee:transform>
		</on-error-propagate>
		<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate" doc:id="b0cb0308-4ad9-4d16-b4a4-dce0b1874109" type="APIKIT:UNSUPPORTED_MEDIA_TYPE" >
			<logger level="ERROR" doc:name="LOG ERROR: Error" doc:id="c34f21f3-d5ff-4166-abf6-3838529644b7" message="#[output application/json&#10;---&#10;{&#10;	Message: error.description,&#10;	CorrelationId: vars.request.correlationID,&#10;	Errors: error&#10;}]" />
			<ee:transform doc:name="Outbound Error Response" doc:id="35e7ebeb-0795-49fb-a193-b37f5eeb41de" >
				<ee:message >
					<ee:set-payload ><![CDATA[%dw 2.0
output application/json
---
{
	statusCode: Mule::p('apikit.errors.unsupportedMediaType.code') as Number,
	reasonPhrase: Mule::p('apikit.errors.unsupportedMediaType.description'),
	response: {
		message: error.description,
		details: error
	}
}]]></ee:set-payload>
				</ee:message>
				<ee:variables >
					<ee:set-variable variableName="httpStatus" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.code') as Number]]></ee:set-variable>
					<ee:set-variable variableName="httpReasonPhrase" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.description')]]></ee:set-variable>
				</ee:variables>
			</ee:transform>
		</on-error-propagate>
		<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate" doc:id="a286cd83-5f67-4025-a009-b7fe1adac2da" type="APIKIT:NOT_IMPLEMENTED" >
			<logger level="ERROR" doc:name="LOG ERROR: Error" doc:id="8262bc37-f777-4a08-addd-1a0a9b0dbd61" message="#[output application/json&#10;---&#10;{&#10;	Message: error.description,&#10;	CorrelationId: vars.request.correlationID,&#10;	Errors: error&#10;}]" />
			<ee:transform doc:name="Outbound Error Response" doc:id="28b65feb-4921-4c30-a3a8-9ca5325c228a" >
				<ee:message >
					<ee:set-payload ><![CDATA[%dw 2.0
output application/json
---
{
	statusCode: Mule::p('apikit.errors.notImplemented.code') as Number,
	reasonPhrase: Mule::p('apikit.errors.notImplemented.description'),
	response: {
		message: error.description,
		details: error
	}
}]]></ee:set-payload>
				</ee:message>
				<ee:variables >
					<ee:set-variable variableName="httpStatus" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.code') as Number]]></ee:set-variable>
					<ee:set-variable variableName="httpReasonPhrase" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.description')]]></ee:set-variable>
				</ee:variables>
			</ee:transform>
		</on-error-propagate>
		<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate" doc:id="ff8b4bb9-7fa5-4443-a1d5-068152e471b6" type="EXPRESSION" >
			<logger level="ERROR" doc:name="LOG ERROR: Error" doc:id="ed05435b-8b0c-4369-ad4b-bb31d3e49026" message="#[output application/json&#10;---&#10;{&#10;	Message: error.description,&#10;	CorrelationId: vars.request.correlationID,&#10;	Errors: error&#10;}]" />
			<ee:transform doc:name="Outbound Error Response" doc:id="635e9aed-56eb-4115-b34b-05fc7e61206f" >
				<ee:message >
					<ee:set-payload ><![CDATA[%dw 2.0
output application/json
---
{
	statusCode: Mule::p('apikit.errors.default.code') as Number,
	reasonPhrase: Mule::p('apikit.errors.default.description'),
	response: {
		message: error.description,
		details: error
	}
}]]></ee:set-payload>
				</ee:message>
				<ee:variables >
					<ee:set-variable variableName="httpStatus" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.code') as Number]]></ee:set-variable>
					<ee:set-variable variableName="httpReasonPhrase" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.description')]]></ee:set-variable>
				</ee:variables>
			</ee:transform>
		</on-error-propagate>
		<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate" doc:id="a13e3ebd-9d13-4ac5-a4d6-0c49e0a9dd28" type="STREAM_MAXIMUM_SIZE_EXCEEDED" >
			<logger level="ERROR" doc:name="LOG ERROR: Error" doc:id="9c96d341-e628-4817-8144-8170f70a8933" message="#[output application/json&#10;---&#10;{&#10;	Message: error.description,&#10;	CorrelationId: vars.request.correlationID,&#10;	Errors: error&#10;}]" />
			<ee:transform doc:name="Outbound Error Response" doc:id="076ab04a-4152-4abe-8612-7b0d64bbbf7f" >
				<ee:message >
					<ee:set-payload ><![CDATA[%dw 2.0
output application/json
---
{
	statusCode: Mule::p('apikit.errors.default.code') as Number,
	reasonPhrase: Mule::p('apikit.errors.default.description'),
	response: {
		message: error.description,
		details: error
	}
}]]></ee:set-payload>
				</ee:message>
				<ee:variables >
					<ee:set-variable variableName="httpStatus" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.code') as Number]]></ee:set-variable>
					<ee:set-variable variableName="httpReasonPhrase" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.description')]]></ee:set-variable>
				</ee:variables>
			</ee:transform>
		</on-error-propagate>
    
</error-handler>

</mule>