<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core" xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd">
	<sub-flow name="auth_artemis-api" doc:id="66fed2f4-cb9e-49a9-b034-d96e51693798" >
		<ee:transform doc:name="payload" doc:id="6a916bc5-00fd-4383-99ec-7b08de99aefa" >
			<ee:message >
				<ee:set-payload ><![CDATA[%dw 2.0
output application/java
---
[{
	"ClientID": Mule::p('secure::artemis.api.https.request.headers.clientId'),
	"ClientSecret": Mule::p('secure::artemis.api.https.request.headers.clientSecret')
}]]]></ee:set-payload>
			</ee:message>
		</ee:transform>
		<set-variable value='#[%dw 2.0&#10;import mask from dw::util::Values&#10;output application/json&#10;---&#10;payload[0] &#10;    mask "ClientID" with "&lt;ClientID&gt;"&#10;    mask "ClientSecret" with "&lt;ClientSecret&gt;"]' doc:name="Artemis API Auth Client" doc:id="76ec1832-b7d1-42ed-991a-08035c946830" variableName="artAuthClient"/>
	</sub-flow>
</mule>
