<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core" xmlns:http="http://www.mulesoft.org/schema/mule/http"
	xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd">
	<sub-flow name="artemis_uda_table" doc:id="6b148a49-5981-4ba3-9094-87ab61a69b8a" >
		<logger level="INFO" doc:name="LOG INFO: Flow Start" doc:id="7a67cb46-08c0-44c1-89cd-eef68abd0c66" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW START",&#10;	Flow: "system-flows\artemis\sf-artemis-api\artemis_uda_table",&#10;	CorrelationId: vars.request.correlationID&#10;}]' />
		<try doc:name="Try Retrieving Table Data" doc:id="3dc00b3c-4054-429b-b166-942f97ef5f7e" >
			<http:request method="GET" doc:name="Artemis API Table" doc:id="467b88c3-9ada-4ff3-bdba-9f83da6e764d" path="/UDA" sendBodyMode="NEVER" config-ref="https_request_artemisapi">
			<http:query-params><![CDATA[#[output application/java
---
{
	database: attributes.queryParams.database,
	schema: attributes.queryParams.schema,
	objectType: attributes.queryParams.objectType,
	objectName: attributes.queryParams.objectName,
	filters: "$(attributes.queryParams.filters default '' as String)"
}]]]></http:query-params>
		</http:request>
			<ee:transform doc:name="payload" doc:id="a7b23312-7316-4f6e-ab49-a0206fcecd2a">
			<ee:message>
				<ee:set-payload><![CDATA[%dw 2.0
output application/java
---
read(payload as String,"application/JSON") as Object]]></ee:set-payload>
			</ee:message>
		</ee:transform>
			<logger level="INFO" doc:name="LOG INFO: Artemis API Table" doc:id="8fb2b867-01a1-401c-99f8-9e5638589a18" message='#[output application/json&#10;---&#10;{&#10;	message: "ARTEMIS API: TABLE GET",&#10;	payload: payload&#10;}]'/>
			<choice doc:name="Is Successful?" doc:id="f07e1adb-8971-4536-acc9-a1fe02260209" >
				<when expression='#[!isEmpty(payload.Table1[0].Error)]'>
					<ee:transform doc:name="Artemis Error" doc:id="52ab50aa-62e9-436a-ad0f-d44148e2272a">
						
						<ee:variables>
							<ee:set-variable variableName="artErrorResponse"><![CDATA[%dw 2.0
output application/java
---
{
	errors: payload.Table1[0].Error
}]]></ee:set-variable>
						</ee:variables>
					</ee:transform>
					<raise-error doc:name="ERROR: Artemis API Table" doc:id="352d0569-e367-485e-a7b2-146c616a9592" type="APP:ARTEMIS_API_TABLE" description="ARTEMIS API: GET TABLE ERROR"/>
				</when>
				<otherwise>
					<ee:transform doc:name="payload" doc:id="1ac85cf6-fd01-41f8-82a7-5fb88940925f" >
						<ee:message >
							<ee:set-payload ><![CDATA[%dw 2.0
output application/java
---
payload]]></ee:set-payload>
						</ee:message>
					</ee:transform>
				</otherwise>
			</choice>
			<error-handler ref="artemis-error-handler" />

		</try>
		<logger level="INFO" doc:name="LOG INFO: Flow End" doc:id="0807906b-7b94-40fe-b6f1-f98372bd2781" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW END",&#10;	Flow: "system-flows\artemis\sf-artemis-api\artemis_uda_table",&#10;	CorrelationId: vars.request.correlationID&#10;}]' />
	</sub-flow>
	<sub-flow name="artemis_uda_stored-procedure" doc:id="dacbc900-caff-4a2c-9294-a88e058de41a" >
		<logger level="INFO" doc:name="LOG INFO: Flow Start" doc:id="c97deaea-8872-404f-9eaf-a25739bb2d75" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW START",&#10;	FlowName: "artemis_uda_stored-procedure",&#10;	CorrelationID: vars.request.correlationID&#10;}]' />
		<try doc:name="Try Executing Stored Procedure" doc:id="c94a6814-574b-4edf-8980-f6039a33c25a" >
			<http:request method="GET" doc:name="Artemis API Stored Procedure" doc:id="4cb0dad6-f647-49a7-9899-dd7e12fed3a1" sendBodyMode="NEVER" config-ref="https_request_artemisapi" path="/UDA">
				<http:query-params ><![CDATA[#[output application/java
---
{
	database: attributes.queryParams.database,
	schema: attributes.queryParams.schema,
	objectType: attributes.queryParams.objectType,
	objectName: attributes.queryParams.objectName,
	parameters: "$(attributes.queryParams.parameters default '' as String)"
}]]]></http:query-params>
			</http:request>
			<ee:transform doc:name="payload" doc:id="7b00bb30-52b8-41f8-a8ff-c782d6b51528" >
				<ee:message >
					<ee:set-payload ><![CDATA[%dw 2.0
output application/java
---
read(payload as String,"application/JSON") as Object]]></ee:set-payload>
				</ee:message>
			</ee:transform>
			<logger level="INFO" doc:name="LOG INFO: Artemis API Stored Procedure" doc:id="463fd3cb-f7a9-404f-81c1-5aec4df3d086" message='#[output application/json&#10;---&#10;{&#10;	message: "ARTEMIS API: STORED PROCEDURE GET",&#10;	payload: payload&#10;}]'/>
			<choice doc:name="Is Successful?" doc:id="139371d1-d5d2-4451-8d2b-72b9fd059449" >
				<when expression='#[!isEmpty(payload.Table1[0].Error)]'>
					<ee:transform doc:name="Artemis Error" doc:id="12c7dd72-fab7-43ea-b18e-54a34e357575" >
						<ee:variables >
							<ee:set-variable variableName="artErrorResponse" ><![CDATA[%dw 2.0
output application/java
---
{
	errors: payload.Table1[0].Error
}]]></ee:set-variable>
						</ee:variables>
					</ee:transform>
					<raise-error doc:name="ERROR: Artemis Error" doc:id="070c8112-9b0e-4a2e-a6e4-5e05a61c6e83" type="APP:ARTEMIS_API_STORED_PROCEDURE" description="ARTEMIS API: STORED PROCEDURE ERROR" />
				</when>
				<otherwise >
					<ee:transform doc:name="payload" doc:id="6a9bc8d1-cded-4b58-ba1b-68e1089f6aa5" >
						<ee:message >
							<ee:set-payload ><![CDATA[output application/java
---
payload]]></ee:set-payload>
						</ee:message>
			<ee:variables >
				<ee:set-variable variableName="httpStatus" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.code') as Number]]></ee:set-variable>
				<ee:set-variable variableName="httpReasonPhrase" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.description')]]></ee:set-variable>
			</ee:variables>
					</ee:transform>
				</otherwise>
			</choice>
			<error-handler ref="artemis-error-handler" />
			
		</try>
		<logger level="INFO" doc:name="LOG INFO: Flow End" doc:id="3283242f-11ea-4aa7-b7e0-ec8be8c231ff" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW END",&#10;	FlowName: "artemis_uda_stored-procedure",&#10;	CorrelationID: vars.request.correlationID&#10;}]' />
	</sub-flow>
	<sub-flow name="artemis_document_get" doc:id="0348cb57-5c9d-4f88-86ef-e4c3b375d216" >
		<logger level="INFO" doc:name="LOG INFO: Flow Start" doc:id="2f989213-bec9-40f6-87ad-0d52763714a3" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW START",&#10;	Flow: "",&#10;	CorrelationId: vars.request.correlationID&#10;}]' />
		<try doc:name="Try Retrieving Document" doc:id="7c69860a-bcdb-403b-aa00-27d163f071a8" >
			<http:request method="GET" doc:name="Artemis API Document" doc:id="1d62d012-46e9-4cd6-84e7-f8757df4fb4a" config-ref="https_request_artemisapi" path="/Document/Get" sendBodyMode="NEVER" >
				<http:headers ><![CDATA[#[output application/java
---
{
	Accept : attributes.headers.accept
}]]]></http:headers>
				<http:query-params ><![CDATA[#[output application/java
---
{
	DocumentID : attributes.queryParams.documentID
}]]]></http:query-params>
			</http:request>
			<logger level="INFO" doc:name="LOG INFO: Artemis API Document" doc:id="d4621f7a-ef68-4e85-93a6-da5ed806e3e7" message='#[output application/json&#10;---&#10;{&#10;	message: "ARTEMIS API: TABLE GET",&#10;	payload: payload&#10;}]'/>
			<choice doc:name="Is Successful?" doc:id="f61cbcdd-4fd8-4399-af6f-36203969e778">
			<when expression='#[payload ~= "There is no row at position 0."]'>
				<ee:transform doc:name="Artemis Error" doc:id="7925a2c0-60f0-4e2b-967e-34599ae56643">
					<ee:message>
					</ee:message>
					<ee:variables>
						<ee:set-variable variableName="artErrorResponse"><![CDATA[%dw 2.0
output application/java
---
{
	errors: "DocumentID not found."
}]]></ee:set-variable>
					</ee:variables>
				</ee:transform>
				<raise-error doc:name="ERROR: Document Not Found" doc:id="defd6896-b1ca-4a79-99be-c74bd460aec6" type="APP:ARTEMIS_API_DOCUMENT_NOT_FOUND" description="ARTEMIS API: DOCUMENT NOT FOUND" />
			</when>
				<when expression='#[(!(payload contains("Unauthorized")) and payload != "There is no row at position 0.") and !(payload contains("PDF"))]'>
					<ee:transform doc:name="Artemis Error" doc:id="8a45c967-85c7-4853-a5d8-b2313b3286be" >
						<ee:message />
						<ee:variables >
							<ee:set-variable variableName="artErrorResponse" ><![CDATA[%dw 2.0
output application/java
---
{
	errors: payload
}]]></ee:set-variable>
						</ee:variables>
					</ee:transform>
					<raise-error doc:name="ERROR: Default" doc:id="425ae0dc-a430-456b-a026-28dcf965455f" type="APP:ARTEMIS_API_DOCUMENT_DEFAULT" description="ARTEMIS API: DEFAULT" />
				</when>
				<otherwise>
				<ee:transform doc:name="payload" doc:id="b894d9bd-2b24-47ea-a716-774277915185">
					<ee:message>
						<ee:set-payload><![CDATA[payload]]></ee:set-payload>
					</ee:message>
				</ee:transform>
			</otherwise>
		</choice>
			<error-handler ref="artemis-error-handler" />
			
		</try>
		<logger level="INFO" doc:name="LOG INFO: Flow End" doc:id="75246374-c7c7-407e-a758-d8120df9f6ec" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW END",&#10;	Flow: vars.flowName,&#10;	CorrelationId: vars.request.correlationID&#10;}]' />
	</sub-flow>
	<sub-flow name="artemis_uda_insert" doc:id="6b015cce-1425-401e-9f8c-505edeb74afa" >
		<logger level="INFO" doc:name="LOG INFO: Flow Start" doc:id="fea8f5e9-d89c-4204-9d95-3fd63a8d13ca" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW START",&#10;	Flow: "systems-flowss\artemis\sf-artemis-api\artemis_uda_insert",&#10;	CorrelationId: vars.request.correlationID&#10;}]' />
		<try doc:name="Try Inserting Payload" doc:id="275dbff0-a03e-442f-9bd9-4efd0f3eefaa" >
			<ee:transform doc:name="Artemis Request" doc:id="ba8177b0-5036-47af-914d-78aa9e5ab8ef">
					
				<ee:variables>
					<ee:set-variable variableName="artInsertRequest"><![CDATA[%dw 2.0
output application/json
---
{
	database: payload[0].database,
	schema: payload[0].schema,
	table: payload[0].table,
	value: payload[0].value
}]]></ee:set-variable>
				</ee:variables>
				</ee:transform>
			<foreach doc:name="For Each Record" doc:id="96b8e1a8-102c-4df4-b831-b8fb2d54da69" collection="#[output application/json&#10;---&#10;vars.artInsertRequest.value]">
				<http:request method="POST" doc:name="Artemis API Insert" doc:id="98cfa27f-003f-4ea0-a4ad-66c92f7107d0" config-ref="https_request_artemisapi" path="/UDA" sendBodyMode="ALWAYS" target="artInsertResponse">
				<http:body><![CDATA[#[output application/json
---
[{
	database: vars.artInsertRequest.database,
	schema: vars.artInsertRequest.schema,
	table: vars.artInsertRequest.table,
	value: [payload]
}]]]]></http:body>
				<http:headers><![CDATA[#[output application/java
---
{
	"Accept": "text/plain",
	"Content-Type": "application/json"
}]]]></http:headers>
				<http:query-params><![CDATA[#[output application/json
---
{
	database: Mule::p('artemis.api.https.request.query.database'),
	schema: Mule::p('artemis.api.https.request.query.schema'),
	objectName: Mule::p('artemis.api.https.request.query.insert.objectName')
}]]]></http:query-params>
			</http:request>
				<logger level="INFO" doc:name="LOG INFO: Artemis API Insert" doc:id="341fe13b-ce8a-4ac6-a182-8076d15891c9" message='#[output application/json&#10;---&#10;{&#10;	message: "ARTEMIS API: TABLE INSERT",&#10;	payload: vars.artInsertResponse.Table&#10;}]'/>
				<choice doc:name="Is Successful?" doc:id="84da829b-5bdf-452b-b19c-46ed18004a8b">
				<when expression='#[vars.artInsertResponse contains("Error")]'>
					<ee:transform doc:name="Artemis Error" doc:id="379a8a30-1279-4ca4-bd2c-15b4c1b287e6">
						<ee:message />
						<ee:variables>
							<ee:set-variable variableName="artErrorResponse"><![CDATA[%dw 2.0
output application/json
---
{
	errors: read(vars.artInsertResponse,"application/JSON").Table[0].Column1
}]]></ee:set-variable>
						</ee:variables>
					</ee:transform>
					<raise-error doc:name="ERROR: Insert Failure" doc:id="bc6fb538-3ce9-4bde-8d97-9fe8b026b877" type="APP:ARTEMIS_API_INSERT_FAILURE" description="ARTEMIS API: INSERT ERROR" />
				</when>
					<otherwise>
						<ee:transform doc:name="payload" doc:id="bf22b5a7-268a-4100-b291-b961dba13150">
						<ee:message>
							<ee:set-payload><![CDATA[%dw 2.0
output application/java
---
{
	message: "SUCCESS: RECORDS POSTED TO ARTEMIS",
	details: read(vars.artInsertResponse,"application/JSON").Table[0].Column1
}]]></ee:set-payload>
						</ee:message>
					</ee:transform>
				</otherwise>
			</choice>
			</foreach>
			<ee:transform doc:name="payload" doc:id="57334389-6208-43bb-ab03-af3a7ba6bb7b">
			<ee:message>
				<ee:set-payload><![CDATA[%dw 2.0
output application/java
---
{
	message: "SUCCESS: RECORDS POSTED TO ARTEMIS",
	details: ""
}]]></ee:set-payload>
			</ee:message>
		</ee:transform>
			<error-handler ref="artemis-error-handler" />
		</try>
		<logger level="INFO" doc:name="LOG INFO: Flow End" doc:id="fe4deb66-3d99-496c-a5d0-1e5a6cedc9a4" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW END",&#10;	Flow: "systems-flowss\artemis\sf-artemis-api\artemis_uda_insert",&#10;	CorrelationId: vars.request.correlationID&#10;}]' />
	</sub-flow>
	
</mule>
