%dw 2.0

type auto_5f229e29_b1ef_4c30_b5d4_dd1afa6bd34c_Output_Variables_sfOpportunityLineItemsResponse = Array<org_mule_runtime_api_message_Message {"typeId": "org.mule.runtime.api.message.Message"}>
type org_mule_runtime_api_message_Message = {|
  payload: OpportunityLineItem {"typeId": "OpportunityLineItem",
  "label": "Opportunity Product"}, 
  attributes: Null
|} {"typeId": "org.mule.runtime.api.message.Message"}
type OpportunityLineItem = {|
  Id?: String {"typeId": "Id"}, 
  OpportunityId?: String, 
  PricebookEntry?: Array<{| Vintage__c?: String {"typeId": "Vintage__c"} |}>, 
  PricebookEntryId?: String, 
  Product2Id?: String, 
  Product_Delivery_Date__c?: Date {"typeId": "Product_Delivery_Date__c"}, 
  Quantity?: Number {"typeId": "Quantity"}, 
  UnitPrice?: Number {"typeId": "UnitPrice"}
|} {"typeId": "OpportunityLineItem",
"label": "Opportunity Product"}





