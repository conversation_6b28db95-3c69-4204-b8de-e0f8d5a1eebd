#%RAML 1.0
title: ERP API
description: ERP API
version: v1
protocols: [ HTTPS ]

uses:
  query-parameters: common/query-parameters.raml

traits:
  client-id-required: !include traits/client-id-required.raml

types:
  SalesforceLeadAndAccount: !include data-types/salesforce-lead-and-account.raml
  SalesforcePurchase: !include data-types/salesforce-purchase.raml
  ArtemisInsert: !include data-types/artemis-insert.raml

/artemis/document:
  get:
    is: [client-id-required]
    description: Get Document for provided DocumentID
    queryParameters:
      documentID:
        type: string
        required: true
    responses:
      200:
        body:
          application/pdf:

/artemis/table:
  get:
    is: [client-id-required]
    description: Get Response from Table
    queryParameters:
      database:
        type: query-parameters.database
        required: true
      objectType:
        type: query-parameters.objectType
        required: true
      schema:
        type: query-parameters.schema
        required: true
      objectName:
        type: query-parameters.objectName
        required: true
      filters:
        type: query-parameters.filters
        required: false
      pageSize:
        type: query-parameters.pageSize
        required: false
      pageNumber:
        type: query-parameters.pageNumber
        required: false

/artemis/stored-procedure:
  get:
    is: [client-id-required]
    description: Get Response from Stored Procedure
    queryParameters:
      database:
        type: query-parameters.database
        required: true
      objectType:
        type: query-parameters.objectType
        required: true
      schema:
        type: query-parameters.schema
        required: true
      objectName:
        type: query-parameters.objectName
        required: true
      parameters:
        type: query-parameters.parameters
        required: false
      pageSize:
        type: query-parameters.pageSize
        required: false
      pageNumber:
        type: query-parameters.pageNumber
        required: false

/artemis/insert:
  post:
    is: [client-id-required]
    description: Post Request to Table
    body:
      application/json:
        type: ArtemisInsert

/salesforce/account/service:
  get:
    is: [client-id-required]
    description: Salesforce Account Services
    queryParameters:
      counterpartyId:
        displayName: Counterparty Id
        description: Artemis Counterparty Id
        type: integer
        required: true

/salesforce/opportunity:
  get:
    is: [client-id-required]
    description: (account artemis counterparty id) and (opportunity artemis confirm id)
    queryParameters:
      opportunityId:
        displayName: Opportunity Id
        description: Salesforce Opportunity Id
        type: string
        required: false
      counterpartyId:
        type: query-parameters.counterpartyId
        required: false
      confirmId:
        type: query-parameters.confirmId
        required: false
      isWon:
        displayName: IsWon
        description: Salesforce Opportunity Is Won
        type: boolean
        default: false
        required: false
      recordTypes:
        displayName: Record Types
        description: Salesforce RecordType List
        required: false
        type: string
        example: "'Commodities', 'Utility Partnerships'"
      leadSources:
        displayName: Lead Sources
        description: Salesforce LeaddSource List
        required: false
        type: string
        example: "'Marketplace','Supplier REach'"
      pricebooks:
        displayName: Pricebooks
        description: Salesforce Pricebook List
        required: false
        type: string
        example: "'Climate Action Portal','LCFS'"
    responses:
      200:
        body:
          application/json:

/salesforce/contact/email:
  get:
    is: [client-id-required]
    description: salesforce contact for provided email
    queryParameters:
      email:
        required: true
        type: query-parameters.email
    responses:
      200:
        body:
          application/json:

/salesforce/file:
  post:
    is: [client-id-required]
    description: attach file at provided folder path to salesforce entity
    responses:
      200:
        body:
          application/json:

/salesforce/lead/id:
  get:
    is: [client-id-required]
    description: Get Lead details for provided query parameters
    body:
      application/json:
        type: SalesforceLeadAndAccount
    responses:
      200:
        body:
          application/json:
            example: !include examples/salesforce-lead-response-example.raml

/salesforce/country:
    get:
      is: [client-id-required]
      description: Retrieves Country Name, Country code, Region, and CurrencyIsocode for provided country
      queryParameters:
        country:
          type: query-parameters.country
          required: false
      responses:
        200:
          body:
            application/json:


/salesforce/purchase:
  post:
    is: [client-id-required]
    description: Post purchase to Salesforce
    body:
      application/json:
        type: SalesforcePurchase

/salesforce/api/tokens:
  get:
    is: [client-id-required]
    description: Get new Access Token and Refresh Token for Salesforce API
    responses:
      200:
          body:
            application/json:
              example:
                {
                  "message": "SALESFORCE API: TOKENS REFRESHED",
                  "success": true
                }
      500:
          body:
            application/json:
              example:
                {
                  "message": "SALESFORCE API: TOKENS NOT REFRESHED",
                  "success": false
                }