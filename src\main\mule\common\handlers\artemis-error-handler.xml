<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core" xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd">
	<error-handler name="artemis-error-handler" doc:id="8b1866fc-ed05-4c82-a244-3ab2d2fdce0e" >
		<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate" doc:id="4b5d0c08-ffb5-42d3-b0b2-8db8ea8e8906" type="APP:ARTEMIS_API_TABLE">
			<logger level="ERROR" doc:name="LOG ERROR: Artemis API Table Error" doc:id="c12c9945-20e4-4db2-8081-f2cf583e6d7b" message='#[output application/json&#10;---&#10;{&#10;	Message: error.description,&#10;	CorrelationId: vars.request.correlationID,&#10;	Errors: vars.artErrorResponse.errors&#10;}]' />
			<ee:transform doc:name="Outbound Error Response" doc:id="0e3c1878-b5dd-4277-99bb-46a4e2ae0dfa" >
				<ee:message >
					<ee:set-payload ><![CDATA[%dw 2.0
output application/json
---
{
	statusCode: Mule::p('apikit.errors.default.code') as Number,
	reasonPhrase: Mule::p('apikit.errors.default.description'),
	response: {
		message: error.description,
		details: vars.artErrorResponse.errors
	}
}]]></ee:set-payload>
				</ee:message>
				<ee:variables >
					<ee:set-variable variableName="httpStatus" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.code') as Number]]></ee:set-variable>
					<ee:set-variable variableName="httpReasonPhrase" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.description')]]></ee:set-variable>
				</ee:variables>
				</ee:transform>
		</on-error-propagate>
		<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate" doc:id="ad4a17f6-b470-42ff-ae7c-1efe45c3570f" type="APP:ARTEMIS_API_STORED_PROCEDURE" >
			<logger level="ERROR" doc:name="LOG ERROR: Artemis API Stored Procedure Error" doc:id="f277504e-7b6d-4300-ad37-6316e64ddf30" message="#[output application/json&#10;---&#10;{&#10;	Message: error.description,&#10;	CorrelationId: vars.request.correlationID,&#10;	Errors: vars.artErrorResponse.errors&#10;}]" />
			<ee:transform doc:name="Outbound Error Response" doc:id="dd7e91d7-f1ce-41e5-9f44-f44d3a6ccc2d" >
				<ee:message >
					<ee:set-payload ><![CDATA[%dw 2.0
output application/json
---
{
	statusCode: Mule::p('apikit.errors.default.code') as Number,
	reasonPhrase: Mule::p('apikit.errors.default.description'),
	response: {
		message: error.description,
		details: vars.artErrorResponse.errors
	}
}]]></ee:set-payload>
				</ee:message>
				<ee:variables >
					<ee:set-variable variableName="httpStatus" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.code') as Number]]></ee:set-variable>
					<ee:set-variable variableName="httpReasonPhrase" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.description')]]></ee:set-variable>
				</ee:variables>
	
			</ee:transform>
		</on-error-propagate>
		<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate" doc:id="a682bc54-2c64-4863-bb6f-de562c84e7c3" type="APP:ARTEMIS_API_DOCUMENT_NOT_FOUND">
			<logger level="ERROR" doc:name="LOG ERROR: Document Not Found" doc:id="70612ba1-cf5f-47f5-a701-75091627823a" message="#[output application/json&#10;---&#10;{&#10;	Message: error.description,&#10;	CorrelationId: vars.request.correlationID,&#10;	Errors: vars.artErrorResponse.errors&#10;}]" />
			<ee:transform doc:name="Outbound Error Response" doc:id="203672d1-2743-4685-acb0-a781ad2ce1b3" >
				<ee:message >
					<ee:set-payload ><![CDATA[%dw 2.0
output application/json
---
{
	statusCode: Mule::p('apikit.errors.notFound.code') as Number,
	reasonPhrase: Mule::p('apikit.errors.notFound.description'),
	response: {
		message: error.description,
		details: vars.artErrorResponse.errors
	}
}]]></ee:set-payload>
				</ee:message>
				<ee:variables >
					<ee:set-variable variableName="httpStatus" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.code') as Number]]></ee:set-variable>
					<ee:set-variable variableName="httpReasonPhrase" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.description')]]></ee:set-variable>
				</ee:variables>
			</ee:transform>
		</on-error-propagate>
		<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate" doc:id="0ca739da-9792-43a1-895f-90ba9b6adc9c" type="APP:ARTEMIS_API_INSERT_FAILURE">
			<logger level="ERROR" doc:name="LOG ERROR: Artemis API Insert Error" doc:id="67e2c8d6-3f47-4d47-82b4-5e96834189eb" message="#[output application/json&#10;---&#10;{&#10;	Message: error.description,&#10;	CorrelationId: vars.request.correlationID,&#10;	Errors: vars.artErrorResponse.errors&#10;}]" />
			<ee:transform doc:name="Outbound Error Response" doc:id="d6861ea4-4d30-42d9-90ae-4e7db4aabba5">
				<ee:message>
					<ee:set-payload><![CDATA[%dw 2.0
output application/json
---
{
	statusCode: Mule::p('apikit.errors.badRequest.code') as Number,
	reasonPhrase: Mule::p('apikit.errors.badRequest.description'),
	response: {
		message: error.description,
		details: vars.artErrorResponse.errors
	}
}]]></ee:set-payload>
				</ee:message>
				<ee:variables >
					<ee:set-variable variableName="httpStatus" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.code') as Number]]></ee:set-variable>
					<ee:set-variable variableName="httpReasonPhrase" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.description')]]></ee:set-variable>
				</ee:variables>
	
			</ee:transform>
		</on-error-propagate>
		<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate" doc:id="24ce9c34-984f-484e-95c0-9df20fec2141" type="APP:ARTEMIS_API_DOCUMENT_DEFAULT" >
			<logger level="ERROR" doc:name="LOG ERROR: Default" doc:id="26b35d51-8526-4b18-9329-35270b8e0fc2" message="#[output application/json&#10;---&#10;{&#10;	Message: error.description,&#10;	CorrelationId: vars.request.correlationID,&#10;	Errors: vars.artErrorResponse.errors&#10;}]" />
			<ee:transform doc:name="Outbound Error Response" doc:id="532cffb5-2625-4d58-b039-ea27fb8b6fce" >
				<ee:message >
					<ee:set-payload ><![CDATA[%dw 2.0
output application/json
---
{
	statusCode: Mule::p('apikit.errors.default.code') as Number,
	reasonPhrase: Mule::p('apikit.errors.default.description'),
	response: {
		message: error.description,
		details: vars.artErrorResponse.errors
	}
}]]></ee:set-payload>
				</ee:message>
				<ee:variables >
					<ee:set-variable variableName="httpStatus" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.code') as Number]]></ee:set-variable>
					<ee:set-variable variableName="httpReasonPhrase" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.description')]]></ee:set-variable>
				</ee:variables>
	
			</ee:transform>
		</on-error-propagate>
		
	</error-handler>
</mule>
