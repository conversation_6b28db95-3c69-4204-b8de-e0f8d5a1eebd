#%RAML 1.0 Trait

headers:
  client_id:
    type: string
    displayName: Client ID
    description: client_id issued by the API Admin/Owner for accessing the API
    required: true
  client_secret:
    type: string
    displayName: Client Secret
    description: client_secret issued by the API Admin/Owner for accessing the API
    required: true
responses:
  401:
    description: Unauthorized or invalid client application credentials
  500:
    description: Error from authorization server