<?xml version='1.0' encoding='UTF-8'?>
<types:mule xmlns:types="http://www.mulesoft.org/schema/mule/types">
  <types:catalog>
    <types:type name="kvpExample" format="json">
      <types:shape format="weave" example="examples/kvpExample-1.json"><![CDATA[%dw 2.0

type kvpExample = {|
  key: String, 
  value: String
|} {"example": "{\\\"key\\\":\\\"key_name\\\",\\\"value\\\":\\\"key_value\\\"}"}

]]></types:shape>
    </types:type>
    <types:type name="stringPayload" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: string]]></types:shape>
    </types:type>
    <types:type name="storeXeroAuthPostExample" format="json">
      <types:shape format="weave" example="examples/storeXeroAuthPostExample.json"><![CDATA[%dw 2.0

type storeXeroAuthPostExample = Array<{|  key: String,   value: String|}> {"example": "[{\\\"key\\\":\\\"grant_type\\\",\\\"value\\\":\\\"refresh_token\\\"},{\\\"key\\\":\\\"scopes\\\",\\\"value\\\":\\\"offline_access accounting.transactions files openid profile email accounting.contacts accounting.settings\\\"},{\\\"key\\\":\\\"tenant_id\\\",\\\"value\\\":\\\"TENANT_ID\\\"},{\\\"key\\\":\\\"client_id\\\",\\\"value\\\":\\\"CLIENT_ID\\\"},{\\\"key\\\":\\\"client_secret\\\",\\\"value\\\":\\\"CLIENT_SECRET\\\"},{\\\"key\\\":\\\"refresh_token\\\",\\\"value\\\":\\\"REFRESH_TOKE\\\"},{\\\"key\\\":\\\"access_token\\\",\\\"value\\\":\\\"ACCESS_TOKEN\\\"}]"}

]]></types:shape>
    </types:type>
    <types:type name="storeXeroAuthResponseExample" format="json">
      <types:shape format="weave" example="examples/storeXeroAuthResponseExample.json"><![CDATA[%dw 2.0

type storeXeroAuthResponseExample = {|
  grant_type: String, 
  scopes: String, 
  tenant_id: String, 
  client_id: String, 
  client_secret: String, 
  refresh_token: String, 
  access_token: String, 
  expire_time: String
|} {"example": "{\\\"grant_type\\\":\\\"GRANT_TYPE\\\",\\\"scopes\\\":\\\"SCOPES\\\",\\\"tenant_id\\\":\\\"TENANT_ID\\\",\\\"client_id\\\":\\\"CLIENT_ID\\\",\\\"client_secret\\\":\\\"CLIENT_SECRET\\\",\\\"refresh_token\\\":\\\"REFRESH_TOKEN\\\",\\\"access_token\\\":\\\"ACCESS_TOKEN\\\",\\\"expire_time\\\":\\\"EXPIRE_TIME\\\"}"}

]]></types:shape>
    </types:type>
    <types:type name="xeroAuthHeaderExample" format="json">
      <types:shape format="weave" example="examples/xeroAuthHeaderExample.json"><![CDATA[%dw 2.0

type xeroAuthHeaderExample = {|
  tenant_id: String, 
  access_token: String
|} {"example": "{\\\"tenant_id\\\":\\\"TENANT_ID\\\",\\\"access_token\\\":\\\"ACCESS_TOKEN\\\"}"}

]]></types:shape>
    </types:type>
    <types:type name="xeroConfirmInvoicesExample" format="json">
      <types:shape format="weave" example="examples/xeroConfirmInvoicesExample.json"><![CDATA[%dw 2.0

type xeroConfirmInvoicesExample = {|
  confirm_id: Number {"typeId": "int"}, 
  invoices: Array<{|    Type: String,     InvoiceID: String,     InvoiceNumber: String,     Reference: String,     Payments: Array<{|      PaymentID: String,       Date: String,       Amount: Number {"typeId": "double"},       Reference: String,       CurrencyRate: Number {"typeId": "double"},       HasAccount: Boolean,       HasValidationErrors: Boolean    |}>, 
    Prepayments: Array<Any>, 
    Overpayments: Array<Any>, 
    AmountDue: Number {"typeId": "double"}, 
    AmountPaid: Number {"typeId": "double"}, 
    SentToContact: Boolean, 
    CurrencyRate: Number {"typeId": "double"}, 
    TotalDiscount: Number {"typeId": "double"}, 
    IsDiscounted: Boolean, 
    HasAttachments: Boolean, 
    HasErrors: Boolean, 
    Attachments: Array<{|      AttachmentID: String,       FileName: String,       Url: String,       MimeType: String,       ContentLength: Number {"typeId": "int"}    |}>, 
    InvoicePaymentServices: Array<Any>, 
    Contact: {|
      ContactID: String, 
      ContactStatus: String, 
      Name: String, 
      EmailAddress: String, 
      BankAccountDetails: String, 
      Addresses: Array<{|        AddressType: String,         City: String,         Region: String,         PostalCode: String,         Country: String,         AddressLine1: String      |}>, 
      Phones: Array<{|        PhoneType: String,         PhoneNumber: String,         PhoneAreaCode: String,         PhoneCountryCode: String      |}>, 
      UpdatedDateUTC: String, 
      ContactGroups: Array<Any>, 
      IsSupplier: Boolean, 
      IsCustomer: Boolean, 
      SalesTrackingCategories: Array<Any>, 
      PurchasesTrackingCategories: Array<Any>, 
      ContactPersons: Array<Any>, 
      HasValidationErrors: Boolean
    |}, 
    DateString: String, 
    Date: String, 
    DueDateString: String, 
    DueDate: String, 
    BrandingThemeID: String, 
    Status: String, 
    LineAmountTypes: String, 
    LineItems: Array<{|      ItemCode: String,       Description: String,       UnitAmount: Number {"typeId": "double"},       TaxType: String,       TaxAmount: Number {"typeId": "double"},       LineAmount: Number {"typeId": "double"},       AccountCode: String,       Item: {|        ItemID: String,         Code: String      |},       Tracking: Array<Any>, 
      Quantity: Number {"typeId": "double"}, 
      DiscountRate: Number {"typeId": "double"}, 
      LineItemID: String, 
      DiscountAmount: Number {"typeId": "double"}, 
      ValidationErrors: Array<Any>
    |}>, 
    SubTotal: Number {"typeId": "double"}, 
    TotalTax: Number {"typeId": "double"}, 
    Total: Number {"typeId": "double"}, 
    UpdatedDateUTC: String, 
    CurrencyCode: String, 
    FullyPaidOnDate: String
  |}>
|} {"example": "{\\\"confirm_id\\\":123456,\\\"invoices\\\":[{\\\"Type\\\":\\\"ACCREC\\\",\\\"InvoiceID\\\":\\\"2325e961-9b19-49bb-bdaf-cea42d96f445\\\",\\\"InvoiceNumber\\\":\\\"24571\\\",\\\"Reference\\\":\\\"| Confirm - 27976\\\",\\\"Payments\\\":[{\\\"PaymentID\\\":\\\"241e9d13-36be-419a-9dfb-4e4103aeefba\\\",\\\"Date\\\":\\\"\\/Date(*************+0000)\\/\\\",\\\"Amount\\\":42570.0,\\\"Reference\\\":\\\"\\\",\\\"CurrencyRate\\\":1.0,\\\"HasAccount\\\":false,\\\"HasValidationErrors\\\":false}],\\\"Prepayments\\\":[],\\\"Overpayments\\\":[],\\\"AmountDue\\\":0.0,\\\"AmountPaid\\\":42570.0,\\\"SentToContact\\\":true,\\\"CurrencyRate\\\":1.0,\\\"TotalDiscount\\\":0.0,\\\"IsDiscounted\\\":true,\\\"HasAttachments\\\":true,\\\"HasErrors\\\":false,\\\"Attachments\\\":[{\\\"AttachmentID\\\":\\\"7b46f6ae-93b8-4d97-a209-6f55767214e7\\\",\\\"FileName\\\":\\\"Invoice 24571.pdf\\\",\\\"Url\\\":\\\"https:\\/\\/api.xero.com\\/api.xro\\/2.0\\/Invoices\\/2325e961-9b19-49bb-bdaf-cea42d96f445\\/Attachments\\/Invoice%2024571.pdf\\\",\\\"MimeType\\\":\\\"application\\/pdf\\\",\\\"ContentLength\\\":86552}],\\\"InvoicePaymentServices\\\":[],\\\"Contact\\\":{\\\"ContactID\\\":\\\"8ea9c815-ba35-47f4-9563-20f709564424\\\",\\\"ContactStatus\\\":\\\"ACTIVE\\\",\\\"Name\\\":\\\"Marathon Power, LLC\\\",\\\"EmailAddress\\\":\\\"<EMAIL>\\\",\\\"BankAccountDetails\\\":\\\"\\\",\\\"Addresses\\\":[{\\\"AddressType\\\":\\\"STREET\\\",\\\"City\\\":\\\"\\\",\\\"Region\\\":\\\"\\\",\\\"PostalCode\\\":\\\"\\\",\\\"Country\\\":\\\"\\\"},{\\\"AddressType\\\":\\\"POBOX\\\",\\\"AddressLine1\\\":\\\"62-01 34th Ave\\\",\\\"City\\\":\\\"Woodside\\\",\\\"Region\\\":\\\"NY\\\",\\\"PostalCode\\\":\\\"11377 USA\\\",\\\"Country\\\":\\\"\\\"}],\\\"Phones\\\":[{\\\"PhoneType\\\":\\\"DEFAULT\\\",\\\"PhoneNumber\\\":\\\"\\\",\\\"PhoneAreaCode\\\":\\\"\\\",\\\"PhoneCountryCode\\\":\\\"\\\"},{\\\"PhoneType\\\":\\\"DDI\\\",\\\"PhoneNumber\\\":\\\"\\\",\\\"PhoneAreaCode\\\":\\\"\\\",\\\"PhoneCountryCode\\\":\\\"\\\"},{\\\"PhoneType\\\":\\\"FAX\\\",\\\"PhoneNumber\\\":\\\"\\\",\\\"PhoneAreaCode\\\":\\\"\\\",\\\"PhoneCountryCode\\\":\\\"\\\"},{\\\"PhoneType\\\":\\\"MOBILE\\\",\\\"PhoneNumber\\\":\\\"\\\",\\\"PhoneAreaCode\\\":\\\"\\\",\\\"PhoneCountryCode\\\":\\\"\\\"}],\\\"UpdatedDateUTC\\\":\\\"\\/Date(*************+0000)\\/\\\",\\\"ContactGroups\\\":[],\\\"IsSupplier\\\":false,\\\"IsCustomer\\\":true,\\\"SalesTrackingCategories\\\":[],\\\"PurchasesTrackingCategories\\\":[],\\\"ContactPersons\\\":[],\\\"HasValidationErrors\\\":false},\\\"DateString\\\":\\\"2022-12-01T00:00:00\\\",\\\"Date\\\":\\\"\\/Date(*************+0000)\\/\\\",\\\"DueDateString\\\":\\\"2022-12-16T00:00:00\\\",\\\"DueDate\\\":\\\"\\/Date(*************+0000)\\/\\\",\\\"BrandingThemeID\\\":\\\"c5b91243-3cb5-43da-a625-70639cbaaf21\\\",\\\"Status\\\":\\\"PAID\\\",\\\"LineAmountTypes\\\":\\\"Inclusive\\\",\\\"LineItems\\\":[{\\\"ItemCode\\\":\\\"REC Wholesale\\\",\\\"Description\\\":\\\"REC - NJ Solar - 2022\\\",\\\"UnitAmount\\\":236.5,\\\"TaxType\\\":\\\"NONE\\\",\\\"TaxAmount\\\":0.0,\\\"LineAmount\\\":42570.0,\\\"AccountCode\\\":\\\"02550\\\",\\\"Item\\\":{\\\"ItemID\\\":\\\"c8cf15b4-4d99-4d5e-acbe-87bc2e111f12\\\",\\\"Code\\\":\\\"REC Wholesale\\\"},\\\"Tracking\\\":[],\\\"Quantity\\\":180.0,\\\"DiscountRate\\\":0.0,\\\"LineItemID\\\":\\\"e59a1d91-d005-40b8-a148-b606d46a2d91\\\",\\\"DiscountAmount\\\":0.0,\\\"ValidationErrors\\\":[]}],\\\"SubTotal\\\":42570.0,\\\"TotalTax\\\":0.0,\\\"Total\\\":42570.0,\\\"UpdatedDateUTC\\\":\\\"\\/Date(*************+0000)\\/\\\",\\\"CurrencyCode\\\":\\\"USD\\\",\\\"FullyPaidOnDate\\\":\\\"\\/Date(*************+0000)\\/\\\"}]}"}

]]></types:shape>
    </types:type>
    <types:type name="xeroInvoicesPayloadExample" format="json">
      <types:shape format="weave" example="examples/xeroInvoicesPayloadExample.json"><![CDATA[%dw 2.0

type xeroInvoicesPayloadExample = {|
  Id: String, 
  Status: String, 
  ProviderName: String, 
  DateTimeUTC: String, 
  Invoices: Array<{|    Type: String,     InvoiceID: String,     InvoiceNumber: String,     Reference: String,     Payments: Array<{|      PaymentID: String,       Date: String,       Amount: Number {"typeId": "double"},       Reference: String,       CurrencyRate: Number {"typeId": "double"},       HasAccount: Boolean,       HasValidationErrors: Boolean    |}>, 
    Prepayments: Array<Any>, 
    Overpayments: Array<Any>, 
    AmountDue: Number {"typeId": "double"}, 
    AmountPaid: Number {"typeId": "double"}, 
    SentToContact: Boolean, 
    CurrencyRate: Number {"typeId": "double"}, 
    TotalDiscount: Number {"typeId": "double"}, 
    IsDiscounted: Boolean, 
    HasAttachments: Boolean, 
    HasErrors: Boolean, 
    Attachments: Array<{|      AttachmentID: String,       FileName: String,       Url: String,       MimeType: String,       ContentLength: Number {"typeId": "int"}    |}>, 
    InvoicePaymentServices: Array<Any>, 
    Contact: {|
      ContactID: String, 
      ContactStatus: String, 
      Name: String, 
      EmailAddress: String, 
      BankAccountDetails: String, 
      Addresses: Array<{|        AddressType: String,         City: String,         Region: String,         PostalCode: String,         Country: String,         AddressLine1: String      |}>, 
      Phones: Array<{|        PhoneType: String,         PhoneNumber: String,         PhoneAreaCode: String,         PhoneCountryCode: String      |}>, 
      UpdatedDateUTC: String, 
      ContactGroups: Array<Any>, 
      IsSupplier: Boolean, 
      IsCustomer: Boolean, 
      SalesTrackingCategories: Array<Any>, 
      PurchasesTrackingCategories: Array<Any>, 
      ContactPersons: Array<Any>, 
      HasValidationErrors: Boolean
    |}, 
    DateString: String, 
    Date: String, 
    DueDateString: String, 
    DueDate: String, 
    BrandingThemeID: String, 
    Status: String, 
    LineAmountTypes: String, 
    LineItems: Array<{|      ItemCode: String,       Description: String,       UnitAmount: Number {"typeId": "double"},       TaxType: String,       TaxAmount: Number {"typeId": "double"},       LineAmount: Number {"typeId": "double"},       AccountCode: String,       Item: {|        ItemID: String,         Code: String      |},       Tracking: Array<Any>, 
      Quantity: Number {"typeId": "double"}, 
      DiscountRate: Number {"typeId": "double"}, 
      LineItemID: String, 
      DiscountAmount: Number {"typeId": "double"}, 
      ValidationErrors: Array<Any>
    |}>, 
    SubTotal: Number {"typeId": "double"}, 
    TotalTax: Number {"typeId": "double"}, 
    Total: Number {"typeId": "double"}, 
    UpdatedDateUTC: String, 
    CurrencyCode: String, 
    FullyPaidOnDate: String
  |}>
|} {"example": "{\\\"Id\\\":\\\"505fbaa1-7b0a-4ae6-8f19-6d3b064973bd\\\",\\\"Status\\\":\\\"OK\\\",\\\"ProviderName\\\":\\\"3Degrees\\\",\\\"DateTimeUTC\\\":\\\"\\/Date(*************)\\/\\\",\\\"Invoices\\\":[{\\\"Type\\\":\\\"ACCREC\\\",\\\"InvoiceID\\\":\\\"2325e961-9b19-49bb-bdaf-cea42d96f445\\\",\\\"InvoiceNumber\\\":\\\"24571\\\",\\\"Reference\\\":\\\"| Confirm - 27976\\\",\\\"Payments\\\":[{\\\"PaymentID\\\":\\\"241e9d13-36be-419a-9dfb-4e4103aeefba\\\",\\\"Date\\\":\\\"\\/Date(*************+0000)\\/\\\",\\\"Amount\\\":42570.00,\\\"Reference\\\":\\\"\\\",\\\"CurrencyRate\\\":1.**********,\\\"HasAccount\\\":false,\\\"HasValidationErrors\\\":false}],\\\"Prepayments\\\":[],\\\"Overpayments\\\":[],\\\"AmountDue\\\":0.00,\\\"AmountPaid\\\":42570.00,\\\"SentToContact\\\":true,\\\"CurrencyRate\\\":1.**********,\\\"TotalDiscount\\\":0.00,\\\"IsDiscounted\\\":true,\\\"HasAttachments\\\":true,\\\"HasErrors\\\":false,\\\"Attachments\\\":[{\\\"AttachmentID\\\":\\\"7b46f6ae-93b8-4d97-a209-6f55767214e7\\\",\\\"FileName\\\":\\\"Invoice 24571.pdf\\\",\\\"Url\\\":\\\"https:\\/\\/api.xero.com\\/api.xro\\/2.0\\/Invoices\\/2325e961-9b19-49bb-bdaf-cea42d96f445\\/Attachments\\/Invoice%2024571.pdf\\\",\\\"MimeType\\\":\\\"application\\/pdf\\\",\\\"ContentLength\\\":86552}],\\\"InvoicePaymentServices\\\":[],\\\"Contact\\\":{\\\"ContactID\\\":\\\"8ea9c815-ba35-47f4-9563-20f709564424\\\",\\\"ContactStatus\\\":\\\"ACTIVE\\\",\\\"Name\\\":\\\"Marathon Power, LLC\\\",\\\"EmailAddress\\\":\\\"<EMAIL>\\\",\\\"BankAccountDetails\\\":\\\"\\\",\\\"Addresses\\\":[{\\\"AddressType\\\":\\\"STREET\\\",\\\"City\\\":\\\"\\\",\\\"Region\\\":\\\"\\\",\\\"PostalCode\\\":\\\"\\\",\\\"Country\\\":\\\"\\\"},{\\\"AddressType\\\":\\\"POBOX\\\",\\\"AddressLine1\\\":\\\"62-01 34th Ave\\\",\\\"City\\\":\\\"Woodside\\\",\\\"Region\\\":\\\"NY\\\",\\\"PostalCode\\\":\\\"11377 USA\\\",\\\"Country\\\":\\\"\\\"}],\\\"Phones\\\":[{\\\"PhoneType\\\":\\\"DEFAULT\\\",\\\"PhoneNumber\\\":\\\"\\\",\\\"PhoneAreaCode\\\":\\\"\\\",\\\"PhoneCountryCode\\\":\\\"\\\"},{\\\"PhoneType\\\":\\\"DDI\\\",\\\"PhoneNumber\\\":\\\"\\\",\\\"PhoneAreaCode\\\":\\\"\\\",\\\"PhoneCountryCode\\\":\\\"\\\"},{\\\"PhoneType\\\":\\\"FAX\\\",\\\"PhoneNumber\\\":\\\"\\\",\\\"PhoneAreaCode\\\":\\\"\\\",\\\"PhoneCountryCode\\\":\\\"\\\"},{\\\"PhoneType\\\":\\\"MOBILE\\\",\\\"PhoneNumber\\\":\\\"\\\",\\\"PhoneAreaCode\\\":\\\"\\\",\\\"PhoneCountryCode\\\":\\\"\\\"}],\\\"UpdatedDateUTC\\\":\\\"\\/Date(*************+0000)\\/\\\",\\\"ContactGroups\\\":[],\\\"IsSupplier\\\":false,\\\"IsCustomer\\\":true,\\\"SalesTrackingCategories\\\":[],\\\"PurchasesTrackingCategories\\\":[],\\\"ContactPersons\\\":[],\\\"HasValidationErrors\\\":false},\\\"DateString\\\":\\\"2022-12-01T00:00:00\\\",\\\"Date\\\":\\\"\\/Date(*************+0000)\\/\\\",\\\"DueDateString\\\":\\\"2022-12-16T00:00:00\\\",\\\"DueDate\\\":\\\"\\/Date(*************+0000)\\/\\\",\\\"BrandingThemeID\\\":\\\"c5b91243-3cb5-43da-a625-70639cbaaf21\\\",\\\"Status\\\":\\\"PAID\\\",\\\"LineAmountTypes\\\":\\\"Inclusive\\\",\\\"LineItems\\\":[{\\\"ItemCode\\\":\\\"REC Wholesale\\\",\\\"Description\\\":\\\"REC - NJ Solar - 2022\\\",\\\"UnitAmount\\\":236.50,\\\"TaxType\\\":\\\"NONE\\\",\\\"TaxAmount\\\":0.00,\\\"LineAmount\\\":42570.00,\\\"AccountCode\\\":\\\"02550\\\",\\\"Item\\\":{\\\"ItemID\\\":\\\"c8cf15b4-4d99-4d5e-acbe-87bc2e111f12\\\",\\\"Code\\\":\\\"REC Wholesale\\\"},\\\"Tracking\\\":[],\\\"Quantity\\\":180.0000,\\\"DiscountRate\\\":0.00,\\\"LineItemID\\\":\\\"e59a1d91-d005-40b8-a148-b606d46a2d91\\\",\\\"DiscountAmount\\\":0.0000,\\\"ValidationErrors\\\":[]}],\\\"SubTotal\\\":42570.00,\\\"TotalTax\\\":0.00,\\\"Total\\\":42570.00,\\\"UpdatedDateUTC\\\":\\\"\\/Date(*************+0000)\\/\\\",\\\"CurrencyCode\\\":\\\"USD\\\",\\\"FullyPaidOnDate\\\":\\\"\\/Date(*************+0000)\\/\\\"}]}"}

]]></types:shape>
    </types:type>
    <types:type name="xeroConfirmsInvoicesExample" format="json">
      <types:shape format="weave" example="examples/xeroConfirmsInvoicesExample.json"><![CDATA[%dw 2.0

type xeroConfirmsInvoicesExample = Array<{|  confirm_id: Number {"typeId": "int"},   invoices: Array<{|    Type: String,     InvoiceID: String,     InvoiceNumber: String,     Reference: String,     Payments: Array<{|      PaymentID: String,       Date: String,       Amount: Number {"typeId": "double"},       Reference: String,       CurrencyRate: Number {"typeId": "double"},       HasAccount: Boolean,       HasValidationErrors: Boolean    |}>, 
    Prepayments: Array<Any>, 
    Overpayments: Array<Any>, 
    AmountDue: Number {"typeId": "double"}, 
    AmountPaid: Number {"typeId": "double"}, 
    SentToContact: Boolean, 
    CurrencyRate: Number {"typeId": "double"}, 
    TotalDiscount: Number {"typeId": "double"}, 
    IsDiscounted: Boolean, 
    HasAttachments: Boolean, 
    HasErrors: Boolean, 
    Attachments: Array<{|      AttachmentID: String,       FileName: String,       Url: String,       MimeType: String,       ContentLength: Number {"typeId": "int"}    |}>, 
    InvoicePaymentServices: Array<Any>, 
    Contact: {|
      ContactID: String, 
      ContactStatus: String, 
      Name: String, 
      EmailAddress: String, 
      BankAccountDetails: String, 
      Addresses: Array<{|        AddressType: String,         City: String,         Region: String,         PostalCode: String,         Country: String,         AddressLine1: String      |}>, 
      Phones: Array<{|        PhoneType: String,         PhoneNumber: String,         PhoneAreaCode: String,         PhoneCountryCode: String      |}>, 
      UpdatedDateUTC: String, 
      ContactGroups: Array<Any>, 
      IsSupplier: Boolean, 
      IsCustomer: Boolean, 
      SalesTrackingCategories: Array<Any>, 
      PurchasesTrackingCategories: Array<Any>, 
      ContactPersons: Array<Any>, 
      HasValidationErrors: Boolean
    |}, 
    DateString: String, 
    Date: String, 
    DueDateString: String, 
    DueDate: String, 
    BrandingThemeID: String, 
    Status: String, 
    LineAmountTypes: String, 
    LineItems: Array<{|      ItemCode: String,       Description: String,       UnitAmount: Number {"typeId": "double"},       TaxType: String,       TaxAmount: Number {"typeId": "double"},       LineAmount: Number {"typeId": "double"},       AccountCode: String,       Item: {|        ItemID: String,         Code: String      |},       Tracking: Array<Any>, 
      Quantity: Number {"typeId": "double"}, 
      DiscountRate: Number {"typeId": "double"}, 
      LineItemID: String, 
      DiscountAmount: Number {"typeId": "double"}, 
      ValidationErrors: Array<Any>
    |}>, 
    SubTotal: Number {"typeId": "double"}, 
    TotalTax: Number {"typeId": "double"}, 
    Total: Number {"typeId": "double"}, 
    UpdatedDateUTC: String, 
    CurrencyCode: String, 
    FullyPaidOnDate: String
  |}>
|}> {"example": "[{\\\"confirm_id\\\":123456,\\\"invoices\\\":[{\\\"Type\\\":\\\"ACCREC\\\",\\\"InvoiceID\\\":\\\"2325e961-9b19-49bb-bdaf-cea42d96f445\\\",\\\"InvoiceNumber\\\":\\\"24571\\\",\\\"Reference\\\":\\\"| Confirm - 27976\\\",\\\"Payments\\\":[{\\\"PaymentID\\\":\\\"241e9d13-36be-419a-9dfb-4e4103aeefba\\\",\\\"Date\\\":\\\"\\/Date(*************+0000)\\/\\\",\\\"Amount\\\":42570.0,\\\"Reference\\\":\\\"\\\",\\\"CurrencyRate\\\":1.0,\\\"HasAccount\\\":false,\\\"HasValidationErrors\\\":false}],\\\"Prepayments\\\":[],\\\"Overpayments\\\":[],\\\"AmountDue\\\":0.0,\\\"AmountPaid\\\":42570.0,\\\"SentToContact\\\":true,\\\"CurrencyRate\\\":1.0,\\\"TotalDiscount\\\":0.0,\\\"IsDiscounted\\\":true,\\\"HasAttachments\\\":true,\\\"HasErrors\\\":false,\\\"Attachments\\\":[{\\\"AttachmentID\\\":\\\"7b46f6ae-93b8-4d97-a209-6f55767214e7\\\",\\\"FileName\\\":\\\"Invoice 24571.pdf\\\",\\\"Url\\\":\\\"https:\\/\\/api.xero.com\\/api.xro\\/2.0\\/Invoices\\/2325e961-9b19-49bb-bdaf-cea42d96f445\\/Attachments\\/Invoice%2024571.pdf\\\",\\\"MimeType\\\":\\\"application\\/pdf\\\",\\\"ContentLength\\\":86552}],\\\"InvoicePaymentServices\\\":[],\\\"Contact\\\":{\\\"ContactID\\\":\\\"8ea9c815-ba35-47f4-9563-20f709564424\\\",\\\"ContactStatus\\\":\\\"ACTIVE\\\",\\\"Name\\\":\\\"Marathon Power, LLC\\\",\\\"EmailAddress\\\":\\\"<EMAIL>\\\",\\\"BankAccountDetails\\\":\\\"\\\",\\\"Addresses\\\":[{\\\"AddressType\\\":\\\"STREET\\\",\\\"City\\\":\\\"\\\",\\\"Region\\\":\\\"\\\",\\\"PostalCode\\\":\\\"\\\",\\\"Country\\\":\\\"\\\"},{\\\"AddressType\\\":\\\"POBOX\\\",\\\"AddressLine1\\\":\\\"62-01 34th Ave\\\",\\\"City\\\":\\\"Woodside\\\",\\\"Region\\\":\\\"NY\\\",\\\"PostalCode\\\":\\\"11377 USA\\\",\\\"Country\\\":\\\"\\\"}],\\\"Phones\\\":[{\\\"PhoneType\\\":\\\"DEFAULT\\\",\\\"PhoneNumber\\\":\\\"\\\",\\\"PhoneAreaCode\\\":\\\"\\\",\\\"PhoneCountryCode\\\":\\\"\\\"},{\\\"PhoneType\\\":\\\"DDI\\\",\\\"PhoneNumber\\\":\\\"\\\",\\\"PhoneAreaCode\\\":\\\"\\\",\\\"PhoneCountryCode\\\":\\\"\\\"},{\\\"PhoneType\\\":\\\"FAX\\\",\\\"PhoneNumber\\\":\\\"\\\",\\\"PhoneAreaCode\\\":\\\"\\\",\\\"PhoneCountryCode\\\":\\\"\\\"},{\\\"PhoneType\\\":\\\"MOBILE\\\",\\\"PhoneNumber\\\":\\\"\\\",\\\"PhoneAreaCode\\\":\\\"\\\",\\\"PhoneCountryCode\\\":\\\"\\\"}],\\\"UpdatedDateUTC\\\":\\\"\\/Date(*************+0000)\\/\\\",\\\"ContactGroups\\\":[],\\\"IsSupplier\\\":false,\\\"IsCustomer\\\":true,\\\"SalesTrackingCategories\\\":[],\\\"PurchasesTrackingCategories\\\":[],\\\"ContactPersons\\\":[],\\\"HasValidationErrors\\\":false},\\\"DateString\\\":\\\"2022-12-01T00:00:00\\\",\\\"Date\\\":\\\"\\/Date(*************+0000)\\/\\\",\\\"DueDateString\\\":\\\"2022-12-16T00:00:00\\\",\\\"DueDate\\\":\\\"\\/Date(*************+0000)\\/\\\",\\\"BrandingThemeID\\\":\\\"c5b91243-3cb5-43da-a625-70639cbaaf21\\\",\\\"Status\\\":\\\"PAID\\\",\\\"LineAmountTypes\\\":\\\"Inclusive\\\",\\\"LineItems\\\":[{\\\"ItemCode\\\":\\\"REC Wholesale\\\",\\\"Description\\\":\\\"REC - NJ Solar - 2022\\\",\\\"UnitAmount\\\":236.5,\\\"TaxType\\\":\\\"NONE\\\",\\\"TaxAmount\\\":0.0,\\\"LineAmount\\\":42570.0,\\\"AccountCode\\\":\\\"02550\\\",\\\"Item\\\":{\\\"ItemID\\\":\\\"c8cf15b4-4d99-4d5e-acbe-87bc2e111f12\\\",\\\"Code\\\":\\\"REC Wholesale\\\"},\\\"Tracking\\\":[],\\\"Quantity\\\":180.0,\\\"DiscountRate\\\":0.0,\\\"LineItemID\\\":\\\"e59a1d91-d005-40b8-a148-b606d46a2d91\\\",\\\"DiscountAmount\\\":0.0,\\\"ValidationErrors\\\":[]}],\\\"SubTotal\\\":42570.0,\\\"TotalTax\\\":0.0,\\\"Total\\\":42570.0,\\\"UpdatedDateUTC\\\":\\\"\\/Date(*************+0000)\\/\\\",\\\"CurrencyCode\\\":\\\"USD\\\",\\\"FullyPaidOnDate\\\":\\\"\\/Date(*************+0000)\\/\\\"}]}]"}

]]></types:shape>
    </types:type>
    <types:type name="stringArrayExample" format="json">
      <types:shape format="weave" example="examples/stringArrayExample.json"><![CDATA[%dw 2.0

type stringArrayExample = Array<String> {"example": "[\\\"string\\\"]"}

]]></types:shape>
    </types:type>
    <types:type name="xeroOnlineInvoicesPayloadExample" format="json">
      <types:shape format="weave" example="examples/xeroOnlineInvoicesPayloadExample.json"><![CDATA[%dw 2.0

type xeroOnlineInvoicesPayloadExample = {|
  Id: String, 
  Status: String, 
  ProviderName: String, 
  DateTimeUTC: String, 
  OnlineInvoices: Array<{| OnlineInvoiceUrl: String |}>
|} {"example": "{\\\"Id\\\":\\\"8ea9e4a5-26b0-4185-bd33-7537ad318f8a\\\",\\\"Status\\\":\\\"OK\\\",\\\"ProviderName\\\":\\\"3D-DEV01\\\",\\\"DateTimeUTC\\\":\\\"\\/Date(1691096235623)\\/\\\",\\\"OnlineInvoices\\\":[{\\\"OnlineInvoiceUrl\\\":\\\"https:\\/\\/in.xero.com\\/jyUFOB3S0drXwfR15BzTq99nTOkQXPRHCQaiLM6V\\\"}]}"}

]]></types:shape>
    </types:type>
    <types:type name="xeroInvoicesDocumentsRequestExample" format="json">
      <types:shape format="weave" example="examples/xeroInvoiceDocumentsRequestExample-1.json"><![CDATA[%dw 2.0

type xeroInvoicesDocumentsRequestExample = Array<{|  invoice_id: String,   invoice_number: String|}> {"example": "[{\\\"invoice_id\\\":\\\"INVOICE_ID\\\",\\\"invoice_number\\\":\\\"INVOICE_NUMBER\\\"}]"}

]]></types:shape>
    </types:type>
    <types:type name="xeroInvoiceDocumentsRequestExample" format="json">
      <types:shape format="weave" example="examples/xeroInvoiceDocumentsRequestExample-2.json"><![CDATA[%dw 2.0

type xeroInvoiceDocumentsRequestExample = {|
  invoice_id: String, 
  invoice_number: String
|} {"example": "{\\\"invoice_id\\\":\\\"INVOICE_ID\\\",\\\"invoice_number\\\":\\\"INVOICE_NUMBER\\\"}"}

]]></types:shape>
    </types:type>
    <types:type name="xeroInvoiceUrlsExample" format="json">
      <types:shape format="weave" example="examples/xeroInvoicesUrlsExample-1.json"><![CDATA[%dw 2.0

type xeroInvoiceUrlsExample = Array<{|  invoice_id: String,   invoice_number: String,   invoice_documents: Array<{| url: String |}>
|}> {"example": "[{\\\"invoice_id\\\":\\\"INVOICE_ID\\\",\\\"invoice_number\\\":\\\"INVOICE_NUMBER\\\",\\\"invoice_documents\\\":[{\\\"url\\\":\\\"INVOICE_URL\\\"}]}]"}

]]></types:shape>
    </types:type>
    <types:type name="confirmListRequestExample" format="json">
      <types:shape format="weave" example="examples/confirmListRequestExample.json"><![CDATA[%dw 2.0

type confirmListRequestExample = Array<{| confirm_id: String |}> {"example": "[{\\\"confirm_id\\\":\\\"CONFIRM_ID\\\"}]"}

]]></types:shape>
    </types:type>
    <types:type name="xeroInvoiceDocumentsPayloadExample" format="json">
      <types:shape format="weave" example="examples/xeroInvoiceDocumentsPayloadExample.json"><![CDATA[%dw 2.0

type xeroInvoiceDocumentsPayloadExample = Array<{|  confirm_id: String,   invoices: Array<{|    invoice_id: String,     invoice_number: String,     contact_id: String,     contact_name: String  |}>
|}> {"example": "[{\\\"confirm_id\\\":\\\"CONFIRM_ID\\\",\\\"invoices\\\":[{\\\"invoice_id\\\":\\\"INVOICE_ID\\\",\\\"invoice_number\\\":\\\"INVOICE_NUMBER\\\",\\\"contact_id\\\":\\\"CONTACT_ID\\\",\\\"contact_name\\\":\\\"CONTACT_NAME\\\"}]}]"}

]]></types:shape>
    </types:type>
    <types:type name="xeroInvoicesDocumentsInfoPayloadExample" format="json">
      <types:shape format="weave" example="examples/xeroInvoicesInfoPayloadExample.json"><![CDATA[%dw 2.0

type xeroInvoicesDocumentsInfoPayloadExample = Array<{|  invoice_id: String,   invoice_number: String,   contact: {|    contact_id: String,     contact_name: String  |},   invoice_documents: Array<{| url: String |}>
|}> {"example": "[{\\\"invoice_id\\\":\\\"INVOICE_ID\\\",\\\"invoice_number\\\":\\\"INVOICE_NUMBER\\\",\\\"contact\\\":{\\\"contact_id\\\":\\\"CONTACT_ID\\\",\\\"contact_name\\\":\\\"CONTACT_NAME\\\"},\\\"invoice_documents\\\":[{\\\"url\\\":\\\"INVOICE_URL\\\"}]}]"}

]]></types:shape>
    </types:type>
    <types:type name="xeroAuthTokensExample" format="json">
      <types:shape format="weave" example="examples/xeroAuthTokensExample.json"><![CDATA[%dw 2.0

type xeroAuthTokensExample = {|
  access_token: String, 
  refresh_token: String
|} {"example": "{\\\"access_token\\\":\\\"ACCESS_TOKEN\\\",\\\"refresh_token\\\":\\\"REFRESH_TOKEN\\\"}"}

]]></types:shape>
    </types:type>
    <types:type name="xeroInvoicesResponsePayloadExample" format="json">
      <types:shape format="weave" example="examples/xeroInvoicesResponsePayloadExample.json"><![CDATA[%dw 2.0

type xeroInvoicesResponsePayloadExample = Array<{|  Id: String,   Status: String,   ProviderName: String,   DateTimeUTC: String,   Invoices: Array<{|    Type: String,     InvoiceID: String,     InvoiceNumber: String,     Reference: String,     Payments: Array<{|      PaymentID: String,       Date: String,       Amount: Number {"typeId": "double"},       Reference: String,       CurrencyRate: Number {"typeId": "double"},       HasAccount: Boolean,       HasValidationErrors: Boolean    |}>, 
    Prepayments: Array<Any>, 
    Overpayments: Array<Any>, 
    AmountDue: Number {"typeId": "double"}, 
    AmountPaid: Number {"typeId": "double"}, 
    SentToContact: Boolean, 
    CurrencyRate: Number {"typeId": "double"}, 
    TotalDiscount: Number {"typeId": "double"}, 
    IsDiscounted: Boolean, 
    HasAttachments: Boolean, 
    HasErrors: Boolean, 
    Attachments: Array<{|      AttachmentID: String,       FileName: String,       Url: String,       MimeType: String,       ContentLength: Number {"typeId": "int"}    |}>, 
    InvoicePaymentServices: Array<Any>, 
    Contact: {|
      ContactID: String, 
      ContactStatus: String, 
      Name: String, 
      EmailAddress: String, 
      BankAccountDetails: String, 
      Addresses: Array<{|        AddressType: String,         City: String,         Region: String,         PostalCode: String,         Country: String,         AddressLine1: String      |}>, 
      Phones: Array<{|        PhoneType: String,         PhoneNumber: String,         PhoneAreaCode: String,         PhoneCountryCode: String      |}>, 
      UpdatedDateUTC: String, 
      ContactGroups: Array<Any>, 
      IsSupplier: Boolean, 
      IsCustomer: Boolean, 
      SalesTrackingCategories: Array<Any>, 
      PurchasesTrackingCategories: Array<Any>, 
      ContactPersons: Array<Any>, 
      HasValidationErrors: Boolean
    |}, 
    DateString: String, 
    Date: String, 
    DueDateString: String, 
    DueDate: String, 
    BrandingThemeID: String, 
    Status: String, 
    LineAmountTypes: String, 
    LineItems: Array<{|      ItemCode: String,       Description: String,       UnitAmount: Number {"typeId": "double"},       TaxType: String,       TaxAmount: Number {"typeId": "double"},       LineAmount: Number {"typeId": "double"},       AccountCode: String,       Item: {|        ItemID: String,         Code: String      |},       Tracking: Array<Any>, 
      Quantity: Number {"typeId": "double"}, 
      DiscountRate: Number {"typeId": "double"}, 
      LineItemID: String, 
      DiscountAmount: Number {"typeId": "double"}, 
      ValidationErrors: Array<Any>
    |}>, 
    SubTotal: Number {"typeId": "double"}, 
    TotalTax: Number {"typeId": "double"}, 
    Total: Number {"typeId": "double"}, 
    UpdatedDateUTC: String, 
    CurrencyCode: String, 
    FullyPaidOnDate: String
  |}>
|}> {"example": "[{\\\"Id\\\":\\\"505fbaa1-7b0a-4ae6-8f19-6d3b064973bd\\\",\\\"Status\\\":\\\"OK\\\",\\\"ProviderName\\\":\\\"3Degrees\\\",\\\"DateTimeUTC\\\":\\\"\\/Date(*************)\\/\\\",\\\"Invoices\\\":[{\\\"Type\\\":\\\"ACCREC\\\",\\\"InvoiceID\\\":\\\"2325e961-9b19-49bb-bdaf-cea42d96f445\\\",\\\"InvoiceNumber\\\":\\\"24571\\\",\\\"Reference\\\":\\\"| Confirm - 27976\\\",\\\"Payments\\\":[{\\\"PaymentID\\\":\\\"241e9d13-36be-419a-9dfb-4e4103aeefba\\\",\\\"Date\\\":\\\"\\/Date(*************+0000)\\/\\\",\\\"Amount\\\":42570.00,\\\"Reference\\\":\\\"\\\",\\\"CurrencyRate\\\":1.**********,\\\"HasAccount\\\":false,\\\"HasValidationErrors\\\":false}],\\\"Prepayments\\\":[],\\\"Overpayments\\\":[],\\\"AmountDue\\\":0.00,\\\"AmountPaid\\\":42570.00,\\\"SentToContact\\\":true,\\\"CurrencyRate\\\":1.**********,\\\"TotalDiscount\\\":0.00,\\\"IsDiscounted\\\":true,\\\"HasAttachments\\\":true,\\\"HasErrors\\\":false,\\\"Attachments\\\":[{\\\"AttachmentID\\\":\\\"7b46f6ae-93b8-4d97-a209-6f55767214e7\\\",\\\"FileName\\\":\\\"Invoice 24571.pdf\\\",\\\"Url\\\":\\\"https:\\/\\/api.xero.com\\/api.xro\\/2.0\\/Invoices\\/2325e961-9b19-49bb-bdaf-cea42d96f445\\/Attachments\\/Invoice%2024571.pdf\\\",\\\"MimeType\\\":\\\"application\\/pdf\\\",\\\"ContentLength\\\":86552}],\\\"InvoicePaymentServices\\\":[],\\\"Contact\\\":{\\\"ContactID\\\":\\\"8ea9c815-ba35-47f4-9563-20f709564424\\\",\\\"ContactStatus\\\":\\\"ACTIVE\\\",\\\"Name\\\":\\\"Marathon Power, LLC\\\",\\\"EmailAddress\\\":\\\"<EMAIL>\\\",\\\"BankAccountDetails\\\":\\\"\\\",\\\"Addresses\\\":[{\\\"AddressType\\\":\\\"STREET\\\",\\\"City\\\":\\\"\\\",\\\"Region\\\":\\\"\\\",\\\"PostalCode\\\":\\\"\\\",\\\"Country\\\":\\\"\\\"},{\\\"AddressType\\\":\\\"POBOX\\\",\\\"AddressLine1\\\":\\\"62-01 34th Ave\\\",\\\"City\\\":\\\"Woodside\\\",\\\"Region\\\":\\\"NY\\\",\\\"PostalCode\\\":\\\"11377 USA\\\",\\\"Country\\\":\\\"\\\"}],\\\"Phones\\\":[{\\\"PhoneType\\\":\\\"DEFAULT\\\",\\\"PhoneNumber\\\":\\\"\\\",\\\"PhoneAreaCode\\\":\\\"\\\",\\\"PhoneCountryCode\\\":\\\"\\\"},{\\\"PhoneType\\\":\\\"DDI\\\",\\\"PhoneNumber\\\":\\\"\\\",\\\"PhoneAreaCode\\\":\\\"\\\",\\\"PhoneCountryCode\\\":\\\"\\\"},{\\\"PhoneType\\\":\\\"FAX\\\",\\\"PhoneNumber\\\":\\\"\\\",\\\"PhoneAreaCode\\\":\\\"\\\",\\\"PhoneCountryCode\\\":\\\"\\\"},{\\\"PhoneType\\\":\\\"MOBILE\\\",\\\"PhoneNumber\\\":\\\"\\\",\\\"PhoneAreaCode\\\":\\\"\\\",\\\"PhoneCountryCode\\\":\\\"\\\"}],\\\"UpdatedDateUTC\\\":\\\"\\/Date(*************+0000)\\/\\\",\\\"ContactGroups\\\":[],\\\"IsSupplier\\\":false,\\\"IsCustomer\\\":true,\\\"SalesTrackingCategories\\\":[],\\\"PurchasesTrackingCategories\\\":[],\\\"ContactPersons\\\":[],\\\"HasValidationErrors\\\":false},\\\"DateString\\\":\\\"2022-12-01T00:00:00\\\",\\\"Date\\\":\\\"\\/Date(*************+0000)\\/\\\",\\\"DueDateString\\\":\\\"2022-12-16T00:00:00\\\",\\\"DueDate\\\":\\\"\\/Date(*************+0000)\\/\\\",\\\"BrandingThemeID\\\":\\\"c5b91243-3cb5-43da-a625-70639cbaaf21\\\",\\\"Status\\\":\\\"PAID\\\",\\\"LineAmountTypes\\\":\\\"Inclusive\\\",\\\"LineItems\\\":[{\\\"ItemCode\\\":\\\"REC Wholesale\\\",\\\"Description\\\":\\\"REC - NJ Solar - 2022\\\",\\\"UnitAmount\\\":236.50,\\\"TaxType\\\":\\\"NONE\\\",\\\"TaxAmount\\\":0.00,\\\"LineAmount\\\":42570.00,\\\"AccountCode\\\":\\\"02550\\\",\\\"Item\\\":{\\\"ItemID\\\":\\\"c8cf15b4-4d99-4d5e-acbe-87bc2e111f12\\\",\\\"Code\\\":\\\"REC Wholesale\\\"},\\\"Tracking\\\":[],\\\"Quantity\\\":180.0000,\\\"DiscountRate\\\":0.00,\\\"LineItemID\\\":\\\"e59a1d91-d005-40b8-a148-b606d46a2d91\\\",\\\"DiscountAmount\\\":0.0000,\\\"ValidationErrors\\\":[]}],\\\"SubTotal\\\":42570.00,\\\"TotalTax\\\":0.00,\\\"Total\\\":42570.00,\\\"UpdatedDateUTC\\\":\\\"\\/Date(*************+0000)\\/\\\",\\\"CurrencyCode\\\":\\\"USD\\\",\\\"FullyPaidOnDate\\\":\\\"\\/Date(*************+0000)\\/\\\"}]}]"}

]]></types:shape>
    </types:type>
    <types:type name="xeroInvoicesAttachmentsExample" format="json">
      <types:shape format="weave" example="examples/xeroInvoicesAttachmentsExample-1.json"><![CDATA[%dw 2.0

type xeroInvoicesAttachmentsExample = Array<{|  invoices: Array<{|    invoice_id: String,     has_attachments: Boolean,     attachments: Array<{|      attachment_id: String,       file_name: String,       url: String,       mime_type: String,       size: Number {"typeId": "int"}    |}>
  |}>
|}> {"example": "[{\\\"invoices\\\":[{\\\"invoice_id\\\":\\\"2325e961-9b19-49bb-bdaf-cea42d96f445\\\",\\\"has_attachments\\\":true,\\\"attachments\\\":[{\\\"attachment_id\\\":\\\"7b46f6ae-93b8-4d97-a209-6f55767214e7\\\",\\\"file_name\\\":\\\"Invoice 24571.pdf\\\",\\\"url\\\":\\\"https:\\/\\/api.xero.com\\/api.xro\\/2.0\\/Invoices\\/2325e961-9b19-49bb-bdaf-cea42d96f445\\/Attachments\\/Invoice%2024571.pdf\\\",\\\"mime_type\\\":\\\"application\\/pdf\\\",\\\"size\\\":86552}]}]}]"}

]]></types:shape>
    </types:type>
    <types:type name="AttachmentsPayloadResponseExample" format="json">
      <types:shape format="weave" example="examples/AttachmentsPayloadResponseExample.json"><![CDATA[%dw 2.0

type AttachmentsPayloadResponseExample = Array<{|  invoice_id: String,   has_attachments: Boolean,   attachments: Array<{|    attachment_id: String,     file_name: String,     url: String,     mime_type: String,     size: String  |}>
|}> {"example": "[{\\\"invoice_id\\\":\\\"INVOICE_ID\\\",\\\"has_attachments\\\":true,\\\"attachments\\\":[{\\\"attachment_id\\\":\\\"ATTACHMENT_ID\\\",\\\"file_name\\\":\\\"FILE_NAME\\\",\\\"url\\\":\\\"URL\\\",\\\"mime_type\\\":\\\"MIME_TYPE\\\",\\\"size\\\":\\\"SIZE\\\"}]}]"}

]]></types:shape>
    </types:type>
    <types:type name="InvoicesPayloadResponseExample" format="json">
      <types:shape format="weave" example="examples/InvoicesPayloadResponseExample.json"><![CDATA[%dw 2.0

type InvoicesPayloadResponseExample = {|
  Invoices: Array<{|    Type: String,     InvoiceID: String,     InvoiceNumber: String,     Reference: String,     Payments: Array<{|      PaymentID: String,       Date: String,       Amount: Number {"typeId": "double"},       Reference: String,       CurrencyRate: Number {"typeId": "double"},       HasAccount: Boolean,       HasValidationErrors: Boolean    |}>, 
    Prepayments: Array<Any>, 
    Overpayments: Array<Any>, 
    AmountDue: Number {"typeId": "double"}, 
    AmountPaid: Number {"typeId": "double"}, 
    SentToContact: Boolean, 
    CurrencyRate: Number {"typeId": "double"}, 
    TotalDiscount: Number {"typeId": "double"}, 
    IsDiscounted: Boolean, 
    HasAttachments: Boolean, 
    HasErrors: Boolean, 
    InvoicePaymentServices: Array<Any>, 
    Contact: {|
      ContactID: String, 
      ContactStatus: String, 
      Name: String, 
      EmailAddress: String, 
      BankAccountDetails: String, 
      Addresses: Array<{|        AddressType: String,         City: String,         Region: String,         PostalCode: String,         Country: String,         AddressLine1: String      |}>, 
      Phones: Array<{|        PhoneType: String,         PhoneNumber: String,         PhoneAreaCode: String,         PhoneCountryCode: String      |}>, 
      UpdatedDateUTC: String, 
      ContactGroups: Array<Any>, 
      IsSupplier: Boolean, 
      IsCustomer: Boolean, 
      SalesTrackingCategories: Array<Any>, 
      PurchasesTrackingCategories: Array<Any>, 
      ContactPersons: Array<Any>, 
      HasValidationErrors: Boolean
    |}, 
    DateString: String, 
    Date: String, 
    DueDateString: String, 
    DueDate: String, 
    BrandingThemeID: String, 
    Status: String, 
    LineAmountTypes: String, 
    LineItems: Array<{|      ItemCode: String,       Description: String,       UnitAmount: Number {"typeId": "double"},       TaxType: String,       TaxAmount: Number {"typeId": "double"},       LineAmount: Number {"typeId": "double"},       AccountCode: String,       Item: {|        ItemID: String,         Code: String      |},       Tracking: Array<Any>, 
      Quantity: Number {"typeId": "double"}, 
      DiscountRate: Number {"typeId": "double"}, 
      LineItemID: String, 
      DiscountAmount: Number {"typeId": "double"}, 
      ValidationErrors: Array<Any>
    |}>, 
    SubTotal: Number {"typeId": "double"}, 
    TotalTax: Number {"typeId": "double"}, 
    Total: Number {"typeId": "double"}, 
    UpdatedDateUTC: String, 
    CurrencyCode: String, 
    FullyPaidOnDate: String
  |}>
|} {"example": "{\\\"Invoices\\\":[{\\\"Type\\\":\\\"ACCREC\\\",\\\"InvoiceID\\\":\\\"2325e961-9b19-49bb-bdaf-cea42d96f445\\\",\\\"InvoiceNumber\\\":\\\"24571\\\",\\\"Reference\\\":\\\"| Confirm - 27976\\\",\\\"Payments\\\":[{\\\"PaymentID\\\":\\\"241e9d13-36be-419a-9dfb-4e4103aeefba\\\",\\\"Date\\\":\\\"\\/Date(*************+0000)\\/\\\",\\\"Amount\\\":42570.00,\\\"Reference\\\":\\\"\\\",\\\"CurrencyRate\\\":1.**********,\\\"HasAccount\\\":false,\\\"HasValidationErrors\\\":false}],\\\"Prepayments\\\":[],\\\"Overpayments\\\":[],\\\"AmountDue\\\":0.00,\\\"AmountPaid\\\":42570.00,\\\"SentToContact\\\":true,\\\"CurrencyRate\\\":1.**********,\\\"TotalDiscount\\\":0.00,\\\"IsDiscounted\\\":true,\\\"HasAttachments\\\":true,\\\"HasErrors\\\":false,\\\"InvoicePaymentServices\\\":[],\\\"Contact\\\":{\\\"ContactID\\\":\\\"8ea9c815-ba35-47f4-9563-20f709564424\\\",\\\"ContactStatus\\\":\\\"ACTIVE\\\",\\\"Name\\\":\\\"Marathon Power, LLC\\\",\\\"EmailAddress\\\":\\\"<EMAIL>\\\",\\\"BankAccountDetails\\\":\\\"\\\",\\\"Addresses\\\":[{\\\"AddressType\\\":\\\"STREET\\\",\\\"City\\\":\\\"\\\",\\\"Region\\\":\\\"\\\",\\\"PostalCode\\\":\\\"\\\",\\\"Country\\\":\\\"\\\"},{\\\"AddressType\\\":\\\"POBOX\\\",\\\"AddressLine1\\\":\\\"62-01 34th Ave\\\",\\\"City\\\":\\\"Woodside\\\",\\\"Region\\\":\\\"NY\\\",\\\"PostalCode\\\":\\\"11377 USA\\\",\\\"Country\\\":\\\"\\\"}],\\\"Phones\\\":[{\\\"PhoneType\\\":\\\"DEFAULT\\\",\\\"PhoneNumber\\\":\\\"\\\",\\\"PhoneAreaCode\\\":\\\"\\\",\\\"PhoneCountryCode\\\":\\\"\\\"},{\\\"PhoneType\\\":\\\"DDI\\\",\\\"PhoneNumber\\\":\\\"\\\",\\\"PhoneAreaCode\\\":\\\"\\\",\\\"PhoneCountryCode\\\":\\\"\\\"},{\\\"PhoneType\\\":\\\"FAX\\\",\\\"PhoneNumber\\\":\\\"\\\",\\\"PhoneAreaCode\\\":\\\"\\\",\\\"PhoneCountryCode\\\":\\\"\\\"},{\\\"PhoneType\\\":\\\"MOBILE\\\",\\\"PhoneNumber\\\":\\\"\\\",\\\"PhoneAreaCode\\\":\\\"\\\",\\\"PhoneCountryCode\\\":\\\"\\\"}],\\\"UpdatedDateUTC\\\":\\\"\\/Date(*************+0000)\\/\\\",\\\"ContactGroups\\\":[],\\\"IsSupplier\\\":false,\\\"IsCustomer\\\":true,\\\"SalesTrackingCategories\\\":[],\\\"PurchasesTrackingCategories\\\":[],\\\"ContactPersons\\\":[],\\\"HasValidationErrors\\\":false},\\\"DateString\\\":\\\"2022-12-01T00:00:00\\\",\\\"Date\\\":\\\"\\/Date(*************+0000)\\/\\\",\\\"DueDateString\\\":\\\"2022-12-16T00:00:00\\\",\\\"DueDate\\\":\\\"\\/Date(*************+0000)\\/\\\",\\\"BrandingThemeID\\\":\\\"c5b91243-3cb5-43da-a625-70639cbaaf21\\\",\\\"Status\\\":\\\"PAID\\\",\\\"LineAmountTypes\\\":\\\"Inclusive\\\",\\\"LineItems\\\":[{\\\"ItemCode\\\":\\\"REC Wholesale\\\",\\\"Description\\\":\\\"REC - NJ Solar - 2022\\\",\\\"UnitAmount\\\":236.50,\\\"TaxType\\\":\\\"NONE\\\",\\\"TaxAmount\\\":0.00,\\\"LineAmount\\\":42570.00,\\\"AccountCode\\\":\\\"02550\\\",\\\"Item\\\":{\\\"ItemID\\\":\\\"c8cf15b4-4d99-4d5e-acbe-87bc2e111f12\\\",\\\"Code\\\":\\\"REC Wholesale\\\"},\\\"Tracking\\\":[],\\\"Quantity\\\":180.0000,\\\"DiscountRate\\\":0.00,\\\"LineItemID\\\":\\\"e59a1d91-d005-40b8-a148-b606d46a2d91\\\",\\\"DiscountAmount\\\":0.0000,\\\"ValidationErrors\\\":[]}],\\\"SubTotal\\\":42570.00,\\\"TotalTax\\\":0.00,\\\"Total\\\":42570.00,\\\"UpdatedDateUTC\\\":\\\"\\/Date(*************+0000)\\/\\\",\\\"CurrencyCode\\\":\\\"USD\\\",\\\"FullyPaidOnDate\\\":\\\"\\/Date(*************+0000)\\/\\\"}]}"}

]]></types:shape>
    </types:type>
    <types:type name="salesforceOpportunitiesClosedWon" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: array
items:
  properties:
    OpportunityID: string
    CaseSafeID: string
    RecordTypeID: string
    OpportunityName: string
    IsWon: boolean
    CloseDate: string
    TotalContractValue: number
    ConfirmID: string
    ProjectID: string
    OpportunityOwnerID: string
    OpportunityOwnerName: string
    OpportunityOwnerIsActive: boolean
    AccountID: string
    CounterpartyName: string
    CorrelationID: string]]></types:shape>
    </types:type>
    <types:type name="erpOpportuntiesClosedWon" format="json">
      <types:shape format="weave" example="examples/artemis/erpOpportunityClosedWon.json"><![CDATA[%dw 2.0

type erpOpportuntiesClosedWon = {|
  OpportunityID: String, 
  OpportunityCaseSafeID: String, 
  OpportunityName: String, 
  IsWon: String, 
  CloseDate: String, 
  TotalContractValue: String, 
  OpportunityOwnerName: String, 
  OpportunityOwnerIsActive: String, 
  RecordTypeID: String, 
  AccountID: String, 
  ConfirmID: String, 
  ProjectID: Null
|} {"example": "{\\\"OpportunityID\\\":\\\"0060W00000rgmSRQAY\\\",\\\"OpportunityCaseSafeID\\\":\\\"0060W00000rgmSRQAY\\\",\\\"OpportunityName\\\":\\\"RECs - Colgate-Palmolive 2017\\\",\\\"IsWon\\\":\\\"true\\\",\\\"CloseDate\\\":\\\"2017O07-27\\\",\\\"TotalContractValue\\\":\\\"76000.0\\\",\\\"OpportunityOwnerName\\\":\\\"Dan Kosciak\\\",\\\"OpportunityOwnerIsActive\\\":\\\"true\\\",\\\"RecordTypeID\\\":\\\"0120W000002AORkQAO\\\",\\\"AccountID\\\":\\\"001d000000FpVmIAAV\\\",\\\"ConfirmID\\\":\\\"9202\\\",\\\"ProjectID\\\":null}"}

]]></types:shape>
    </types:type>
    <types:type name="ArtemisDocumentExample" format="json">
      <types:shape format="weave" example="examples/ArtemisDocumentExample.json"><![CDATA[%dw 2.0

type ArtemisDocumentExample = {|
  Path: String, 
  FileName: String, 
  Extension: String, 
  MimeType: String, 
  Size: String, 
  DateCreated: String, 
  DateModified: String, 
  DateLastAccessed: String
|} {"example": "{\\\"Path\\\":\\\"\\\\\\\\\\\\\\\\server2.luna.local\\\\\\\\Atlantis\\\\\\\\CP_Files\\\\\\\\SupplierREach\\\\\\\\test.csv\\\",\\\"FileName\\\":\\\"test.csv\\\",\\\"Extension\\\":\\\".csv\\\",\\\"MimeType\\\":\\\"application\\/csv\\\",\\\"Size\\\":\\\"115\\\",\\\"DateCreated\\\":\\\"11\\/27\\/2023 5:34:10 PM\\\",\\\"DateModified\\\":\\\"11\\/27\\/2023 5:34:10 PM\\\",\\\"DateLastAccessed\\\":\\\"11\\/27\\/2023 5:34:10 PM\\\"}"}

]]></types:shape>
    </types:type>
    <types:type name="supplierReachLeadCreateResponse" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    LeadName:
        type: string
    LeadCreated:
        type: boolean
    ProductsCsvCreated:
        type: boolean
    ProductsCsvAttachedToLead:
        type: boolean]]></types:shape>
    </types:type>
    <types:type name="climatePortalSupplierReachPurchase" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
  Company:
    type: string
    required: true
  FirstName: 
    type: string
    required: true
  LastName:
    type: string
    required: true
  Email: 
    type: string
    required: true
  Phone:
    type: string
    required: false
  Lead_Source__c:
    type: string
    required: false
    default: "Supplier REach"
  Status:
    type: string
    required: false
    default: "Qualified"
  Product__c:
    type: array
    required: false
    default: ["Supplier REach"]
  RecordTypeId:
    type: string
    required: false
    default: "012Du0000004N6wIAE"
  CurrencyIsoCode:
    type: string
    required: false
    default: "USD"
  Climate_Portal_Customer_ID__c:
    type: number
    required: false
  Climate_Portal_Account_ID__c:
    type: number
    required: false
  Climate_Portal_Artemis_ID__c:
    type: number
    required: false
  Supplier_REach_Transaction_Date__c:
    type: number
    required: true
  Products:
    type: array
    items:
      properties:
        Name:
          type: string
          required: true
        Volume:
          type: integer
          required: true
        PurchaseOption:
          type: string
          required: true]]></types:shape>
    </types:type>
    <types:type name="climatePortalMarketplacePurchase" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
  Company:
    type: string
    required: true
  FirstName: 
    type: string
    required: true
  LastName:
    type: string
    required: true
  Email: 
    type: string
    required: true
  Phone:
    type: string
    required: false
  Lead_Source__c:
    type: string
    required: false
    default: "Inbound"
  Status:
    type: string
    required: false
    default: "Qualified"
  Product__c:
    type: array
    required: false
    default: ["RECs","Carbon"]
  RecordTypeId:
    type: string
    required: false
    default: "0120W000001USmhQAG"
  CurrencyIsoCode:
    type: string
    required: false
    default: "USD"
  Climate_Portal_Customer_ID__c:
    type: number
    required: true
  Climate_Portal_Account_ID__c:
    type: number
    required: true
  Climate_Portal_Artemis_ID__c:
    type: number
    required: true
  Marketplace_Transaction_Date__c:
    type: number
    required: true
  Products:
    type: array
    items:
      properties:
        Name:
          type: string
          required: true
        Volume:
          type: integer
          required: true
        PurchaseOption:
          type: string
          required: true]]></types:shape>
    </types:type>
    <types:type name="salesforceMarketplaceLeadRequest" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
  Id:
    type: string
    required: false
  IsConverted:
    type: boolean
    required: true
  Climate_Action_Portal_Contact_ID__c:
    type: integer
    required: false
  Climate_Action_Portal_Account_ID__c:
    type: integer
    required: false
  FirstName: 
    type: string
    required: true
  LastName:
    type: string
    required: true
  Email: 
    type: string
    required: true
  Phone:
    type: string
    required: false
  Company:
    type: string
    required: true
  Street:
    type: string
    required: true
  City:
    type: string
    required: true
  State:
    type: string
    required: true
  PostalCode:
    type: string
    required: true
  Country:
    type: string
    required: true
  RecordTypeId: 
    type: string
    required: true
  Status: 
    type: string
    required: true
  LeadSource: 
    type: string
    required: true
  OwnerId: 
    type: string
    required: true
  Account_Name_If_Already_Exists__c: 
    type: string
    required: false]]></types:shape>
    </types:type>
    <types:type name="climatePortalSupplierReachPurchaseProducts" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
  Products:
    type: array
    items:
      properties:
        Name:
          type: string
          required: true
        Volume:
          type: integer
          required: true
        PurchaseOption:
          type: string
          required: true]]></types:shape>
    </types:type>
    <types:type name="PAYLOAD_String" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: string]]></types:shape>
    </types:type>
    <types:type name="artemisApiDocumentList" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    Files:
        type: array
        items:
          properties:
            Path: string
            FileName: string
            Extension: string
            MimeType: string
            Size: integer
            DateCreated: string
            DateModified: string
            DateLastAccessed: string]]></types:shape>
    </types:type>
    <types:type name="FileInfo" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
  FileName: string
  Extension: string
  MimeType: string
  Size: integer
  Path: string
  DateCreated: string]]></types:shape>
    </types:type>
    <types:type name="PAYLOAD_Boolean" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: boolean]]></types:shape>
    </types:type>
    <types:type name="requestPayload" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    method: string
    requestUri: string
    headers: 
      type: object
      properties:
        accept?: string
        content-type?: string
    uriParameters: object
    queryParameters: object
    body?: object]]></types:shape>
    </types:type>
    <types:type name="PAYLOAD_Object" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: string]]></types:shape>
    </types:type>
    <types:type name="supplierReachPurchaseResponse" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    LeadName: string
    LeadCreated: boolean
    ProductsCsvCreated: boolean
    ProductsCsvAttachedToLead: boolean]]></types:shape>
    </types:type>
    <types:type name="SalesforceProduct" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: array
items:
  properties:
    ProductID: string
    ProductName: string
    Family: string
    FamilyBU: string
    IsCommodityProduct: boolean
    IsGreenE: boolean
    Vintage: number
    Country: string 
    CurrencyIsoCode: string
    ProductCode: string
    ProductBusinessLine: string
    IsActive: boolean
    IsArchived: boolean
    IsDeleted: boolean]]></types:shape>
    </types:type>
    <types:type name="SalesforceAccount" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: array
items:
  properties:
    AccountID: string
    AccountName: string
    AccountOwnerID: string
    AccountOwnerName: string
    AccountOwnerIsActive: boolean
    AccountCounterpartyID: number
    CounterpartyID: number
    CaseSafeID: string
    EnterpriseID: string
    ]]></types:shape>
    </types:type>
    <types:type name="SalesforceEntityDocumentResponse" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: array
items:
  properties:
    ContentDocumentId: string]]></types:shape>
    </types:type>
    <types:type name="SalesforceDocumentsResponse" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: array
items:
  properties:
    project_id: number
    filename: string
    filesize: number
    mimetype: string
    created_at: string
    document_guid: string
    document_type: string]]></types:shape>
    </types:type>
    <types:type name="SalesforceAccountOpportunityIdList" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: array
items:
  properties:
    Id: string]]></types:shape>
    </types:type>
    <types:type name="PAYLOAD_Array_String" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: array]]></types:shape>
    </types:type>
    <types:type name="SalesforceRecordIds" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: array
items:
  properties:
    RecordId: string]]></types:shape>
    </types:type>
    <types:type name="EntityInfo" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    Message:
      type: string
    Entity:
      type: object
      properties:
        Object:: 
          type: string
        Id: 
          type: string
        Name: 
          type: string
        LeadSource:
          type: string
        IsCreated:
          type: boolean
          default: false]]></types:shape>
    </types:type>
    <types:type name="MarketplaceLeadRequest" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
  Climate_Action_Portal_Contact_ID__c:
    type: integer
    required: false
  Climate_Action_Portal_Account_ID__c:
    type: integer
    required: false
  FirstName: 
    type: string
    required: true
  LastName: 
    type: string
    required: true
  Email: 
    type: string
    required: true
  Phone: 
    type: string
    required: true
  Title: 
    type: string
    required: true
  Company: 
    type: string
    required: true
  Street: 
    type: string
    required: true
  City: 
    type: string
    required: true
  State: 
    type: string
    required: true
  PostalCode: 
    type: string
    required: true
  Country: 
    type: string
    required: true
  RecordTypeId: 
    type: string
    required: true
    default: "012Jw000002JZXBIA4"
  Status: 
    type: string
    required: true
    default: "Qualified"
  LeadSource: 
    type: string
    required: true
    default: "Marketplace"
  OwnerId: 
    type: string
    required: true
    default: "0050W000007xwxfQAA"
  Account_Name_If_already_exists__c: 
    type: string
    required: false]]></types:shape>
    </types:type>
    <types:type name="LeadRequest" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    Lead:
      type: object
      required: true         
    Account:
      type: object
      required: false
    Contact:
      type: object
      required: false
    Opportunity:
      type: object
      required: false
    OpportunityLineItems:
      type: array
      required: false
      items: 
        type: object
     ]]></types:shape>
    </types:type>
    <types:type name="PAYLOAD_Array_Object" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: array
items:
  properties:
    type: object]]></types:shape>
    </types:type>
    <types:type name="sfOpportunityLineItems" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: array
items:
  properties:
    Quantity:
      type: number
      required: true
    UnitPrice:
      type: number
      required: true
    TotalPrice:
      type: number
      required: true
    OpportunityId:
      type: string
      required: true
    PricebookEntryId:
      type: string
      required: true
    Product2Id:
      type: string
      required: true
    ]]></types:shape>
    </types:type>
    <types:type name="cpMarketplaceLineItems" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: array
items:
  properties:
    ProductId:
      type: integer
      required: true
    Quantity:
      type: integer
      required: true
    UnitPrice:
      type: number
      multipleOf: 0.01
      required: true
    TotalPrice:
      type: number
      multipleOf: 0.01
      required: true
    CurrrencyIsoCode:
      type: string
      required: true]]></types:shape>
    </types:type>
    <types:type name="sfPricebookEntry" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: array
items:
  properties:
    Id:
      type: string
    Climate_action_Portal_Product_ID__c: 
      type: string
    Product2Id:
      type: string
    Vintage__c:
      type: string]]></types:shape>
    </types:type>
    <types:type name="Request" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    method:
      type: string
    path:
      type: string
    uriParameters?:
      type: object
    queryParameters?:
      type: object
    correlationID:
      type: string]]></types:shape>
    </types:type>
    <types:type name="salesforcePurchaseRequest" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType

properties:
  Lead:
    type: object
    required: true
  Account:
    type: object
    required: false
  Contact:
    type: object
    required: false
  Opportunity:
    type: object
    required: false
  OpportunityLineItems:
    type: array
    required: false
  Files:
    type: array
    required: false
    items:
      properties:]]></types:shape>
    </types:type>
    <types:type name="sfMarketplacePurchase" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
  PurchaseType:
    type: string
    required: true
  Lead:
    type: object
    required: true
    properties:
      Climate_Action_Portal_Contact_ID__c?:
        type: integer | nil
        required: false
      Climate_Action_Portal_Account_ID__c?:
        type: integer | nil
        required: false
      FirstName:
        type: string
        required: true
      LastName:
        type: string
        required: true
      Email:
        type: string
        required: true
      Phone?:
        type: string | nil
        required: false
      Title?:
        type: string | nil
        required: false
      Company:
        type: string
        required: true
      Street?:
        type: string | nil
        required: false
      City?:
        type: string | nil
        required: false
      State?:
        type: string | nil
        required: false
      PostalCode?:
        type: string | nil
        required: false
      Country?:
        type: string | nil
        required: false
      RecordTypeId:
        type: string
        required: true
      Status:
        type: string
        required: true
      LeadSource:
        type: string
        required: true
      OwnerId:
        type: string
        required: true
      Account_Name_If_Already_Exists__c:
        type: string | nil
        required: false
  Account:
    type: object
    required: true
    properties:
      Climate_Action_Portal_Account_ID__c?:
        type: integer | nil
        required: false
      Id:
        type: string
        required: true
      Name:
        type: string
        required: true
      BillingStreet?:
        type: string | nil
        required: false
      BillingCity?:
        type: string | nil
        required: false
      BillingState?:
        type: string | nil
        required: false
      BilPostalCode?:
        type: string | nil
        required: false
      BillingCountry?:
        type: string | nil
        required: false
      AccountSource:
        type: string
        required: true
      OwnerId:
        type: string
        required: true
  Contact:
    type: object
    required: true
    properties:
      Climate_Action_Portal_Contact_ID__c?:
        type: integer | nil
        required: false
      Id:
        type: string
        required: true
      FirstName:
        type: string
        required: true
      LastName:
        type: string
        required: true
      Email:
        type: string
        required: true
      Phone?:
        type: string | nil
        required: false
      Title?:
        type: string | nil
        required: false
      OwnerId:
        type: string
        required: true
  Opportunity:
    type: object
    required: true
    properties:
      Name:
        type: string
        required: true
      Counterparty_Name__c?:
        type: string | nil
        required: false
      Counterparty_NameLookup__c?:
        type: string | nil
        required: false
      CP_Street__c?:
        type: string | nil
        required: false
      CP_City__c?:
        type: string | nil
        required: false
      CP_State__c?:
        type: string | nil
        required: false
      CP_Zip_Postal_Code__c?:
        type: string | nil
        required: false
      CP_Mailing_Country__c?:
        type: string | nil
        required: false
      Billing_Street__c?:
        type: string | nil
        required: false
      Billing_City__c?:
        type: string | nil
        required: false
      Billing_State__c?:
        type: string | nil
        required: false
      Billing_Zip_Postal_Code__c?:
        type: string | nil
        required: false
      Billing_Country__c?:
        type: string | nil
        required: false
      Region__c:
        type: string
        required: true
      LeadSource:
        type: string
        required: true
      Type:
        type: string
        required: true
      CloseDate:
        type: date-only
        required: true
      StageName:
        type: string
        required: true
      Online_Transaction__c:
        type: boolean
        required: true
      RecordTypeId:
        type: string
        required: true
      CurrencyIsoCode:
        type: string
        required: true
      Pricebook2Id:
        type: string
        required: true
      Stripe_Charge_ID__c:
        type: string
        required: true
      OwnerId:
        type: string
        required: true
  OpportunityLineItems:
    type: array
    required: true
    items:
      type: object
      properties:
        ProductId:
          type: integer
          required: true
        Quantity:
          type: integer
          required: true
        UnitPrice:
          type: number
          multipleOf: 0.01
          required: true
        TotalPrice:
          type: number
          multipleOf: 0.01
          required: true
        CurrencyIsoCode:
          type: string
          required: true]]></types:shape>
    </types:type>
    <types:type name="sfLeadRequest" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    Lead:
      properties:
        Id:
          type: string
        Name:
          type: string
        Email:
          type: string
        LeadSource:
          type: string
        RecordTypeId:
          type: string
        Climate_Action_Portal_Account_ID__c:
          type: string
        Climate_Action_Portal_Contact_ID__c:
          type: string
    Account:
      properties:
        Id:
          type: string
        Climate_Action_Portal_Account_ID__c:
          type: string
    Contact:
      properties:
        Id:
          type: string
        Climate_Action_Portal_Contact_ID__c:
          type: string
    Opportunity:
      properties:
        Id:
          type: string
        ]]></types:shape>
    </types:type>
    <types:type name="sfMarketplaceLeadRequest" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
    type: object
    properties:
      Climate_Action_Portal_Contact_ID__c?:
        type: integer | nil
        required: false
      Climate_Action_Portal_Account_ID__c?:
        type: integer | nil
        required: false
      FirstName:
        type: string
        required: true
      LastName:
        type: string
        required: true
      Email:
        type: string
        required: true
      Phone?:
        type: string | nil
        required: false
      Title?:
        type: string | nil
        required: false
      Company:
        type: string
        required: true
      Street?:
        type: string | nil
        required: false
      City?:
        type: string | nil
        required: false
      State?:
        type: string | nil
        required: false
      PostalCode?:
        type: string | nil
        required: false
      Country?:
        type: string | nil
        required: false
      RecordTypeId:
        type: string
        required: true
      Status:
        type: string
        required: true
      LeadSource:
        type: string
        required: true
      OwnerId:
        type: string
        required: true
      Account_Name_If_Already_Exists__c:
        type: string | nil
        required: false]]></types:shape>
    </types:type>
    <types:type name="sfContactEmailDomainAccounts" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
  Contacts:
    type: array
    items:
      properties:
        ContactID:
          type: string
        AccountID:
          type: string
        FirstName:
          type: string
        LastName:
          type: string
        Email:
          type: string
        EmailDomain:
          type: string
  EmailDomainAccounts:
    type: array]]></types:shape>
    </types:type>
    <types:type name="ContactResponse" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: array
items:
  properties:
        ContactID:
          type: string
        AccountID:
          type: string
        FirstName:
          type: string
        LastName:
          type: string
        Email:
          type: string
        EmailDomain:
          type: string]]></types:shape>
    </types:type>
    <types:type name="EmailDomainAccountsResponse" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: array
items:
  properties:
    EmailDomain:
      type: string
    AccountID:
      type: string]]></types:shape>
    </types:type>
    <types:type name="salesforceOpportunityRequest" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
  Id:
    type: string
    required: false
  Name: 
    type: string
    required: true
  Counterparty_Name__c:
    type: integer
    required: false
  Counterparty_NameLookup__c:
    type: integer
    required: false
  CP_Street__c:
    type: string
    required: true
  CP_City__c:
    type: string
    required: true
  CP_State__c:
    type: string
    required: true
  CP_Zip_Postal_Code__c:
    type: string
    required: true
  CP_Country__c:
    type: string
    required: true
  Billing_Street__c:
    type: string
    required: true
  Billing_City__c:
    type: string
    required: true
  Billing_State__c:
    type: string
    required: true
  Billing_Zip_Postal_Code__c:
    type: string
    required: true
  Billing_Country__c:
    type: string
    required: true
  Region__c:
    type: string
    required: true
  LeadSource: 
    type: string
    required: true  
  Type: 
    type: string
    required: true
  CloseDate: 
    type: date-only
    required: true
  StageName: 
    type: string
    required: true
  Online_Transaction__c: 
    type: boolean
    required: false
  RecordTypeId: 
    type: string
    required: true
  CurrencyIsoCode: 
    type: string
    required: true
  Pricebook2Id: 
    type: string
    required: true
  Stripe_Charge_ID__c: 
    type: string
    required: true
  OwnerId: 
    type: string
    required: true]]></types:shape>
    </types:type>
    <types:type name="salesforceOpportunityLineItemsRequest" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: array
items:
  properties:
    Id:
      type: string
      required: false
    OpportunityId:
      type: string
      required: false
    Product2Id: 
      type: string
      required: true 
    Quantity: 
      type: integer
      required: true  
    UnitPrice: 
      type: number
      multipleOf: 0.01
      required: true
    TotalPrice: 
      type: number
      multipleOf: 0.01
      required: true
    CurrencyIsoCode:
      type: string
      required: true    
    Product_Delivery_Date__c:
      type: date-only
      required: true]]></types:shape>
    </types:type>
    <types:type name="salesforceMarketplaceAccountRequest" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
  Climate_Action_Portal_Account_ID__c: 
    type: integer
    required: false  
  Id:
    type: string
    required: false
  Name:
    type: string
    required: true
  BillingStreet:
    type: string
    required: false
  BillingCity:
    type: string
    required: false
  BillingState:
    type: string
    required: false
  BillingPostalCode:
    type: string
    required: false
  BillingCountry:
    type: string
    required: true
  AccountSource:
    type: string
    required: true
  OwnerId:
    type: string
    required: true]]></types:shape>
    </types:type>
    <types:type name="salesforceMarketplaceContactRequest" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
  Climate_Action_Portal_Contact_ID__c: 
    type: integer
    required: false
    default: 0  
  Id:
    type: string
    required: false
  FirstName:
    type: string
    required: true
  LastName:
    type: string
    required: true
  Email:
    type: string
    required: true
  Phone:
    type: string
    required: false
  Title:
    type: string
    required: false
  OwnerId:
    type: string
    required: true]]></types:shape>
    </types:type>
    <types:type name="salesforceSupplierREachLeadRequest" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
  Climate_Action_Portal_Contact_ID__c:
    type: number
    required: true
  Climate_Action_Portal_Account_ID__c:
    type: number
    required: true
  Artemis_Counterparty_ID__c:
    type: number
    required: true
  FirstName: 
    type: string
    required: true
  LastName:
    type: string
    required: true
  Email: 
    type: string
    required: true
  Phone:
    type: string
    required: false
  Company:
    type: string
    required: true
  Product__c:
    type: array
    required: false
  Supplier_REach_Transaction_Date__c:
    type: date-only
    required: true
  CurrencyIsoCode:
    type: string
    required: false
  RecordTypeId:
    type: string
    required: false
  Status:
    type: string
    required: false
  LeadSource:
    type: string
    required: false
  OwnerId:
    type: string
    required: true]]></types:shape>
    </types:type>
    <types:type name="salesforceSupplierREachFileRequest" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: array
items:
  properties:
    Entity:
      type: string
      required: true
    Title:
      type: string
      required: true
    FileName:
      type: string
      required: true
    FolderPath:
      type: string
      required: true
    Tags:
      type: string
      required: false
    Content:
      type: array
      required: true
      items:
        properties:
          Name:
            type: string
            required: true
          Volume:
            type: integer
            required: true
          PurchaseOption:
            type: string
            required: true]]></types:shape>
    </types:type>
    <types:type name="salesforceSupplierREachContactRequest" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
  Climate_Action_Portal_Contact_ID__c: 
    type: integer
    required: false  
  Id:
    type: string
    required: false]]></types:shape>
    </types:type>
    <types:type name="salesforceSupplierREachAccountRequest" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
  Climate_Action_Portal_Account_ID__c: 
    type: integer
    required: false  
  Id:
    type: string
    required: false]]></types:shape>
    </types:type>
    <types:type name="sfLeadChangesResponse" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
  Id:
    type: string
  ChangeType:
    type: string
  Changes:
    type: object
  SalesforceTransaction:
    type: object
    properties:
      Key:
        type: string
      Timestamp:
        type: integer]]></types:shape>
    </types:type>
    <types:type name="salesforceMarketplaceLeadChanges" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    Id:
      type: string
    Status:
      type: string
    Account_Name_If_already_exists__c:
      type: string]]></types:shape>
    </types:type>
    <types:type name="Errors" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: array
items:
  properties:
    description:
      type: string
      required: true  ]]></types:shape>
    </types:type>
    <types:type name="salesforceMarketplacePricebook" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: array
items:
  properties:
    Id:
      type: string
    Climate_Action_Portal_Product_ID__c:
      type: integer
    Product2Id:
      type: string
    Vintage__c:
      type: string]]></types:shape>
    </types:type>
    <types:type name="salesforceMarketplaceOpportunityLineItems" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: array
items:
  properties:
    Id:
      type: string
      required: false
    UnitPrice:
      type: number
    OpportunityId:
      type: string
    Product_Delivery_Date__c:
      type: date-only
    PricebookEntryId:
      type: string
    Product2Id:
      type: string
    Vintage_Year__c:
      type: string  ]]></types:shape>
    </types:type>
    <types:type name="ObjectStoreEntry" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    key:
      type: string
    value:
      type: object
      required: false]]></types:shape>
    </types:type>
    <types:type name="salesforceErrors" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    description:
      type: string
      required: true]]></types:shape>
    </types:type>
    <types:type name="sfErrorsResponse" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    StatusCode:
      type: string
    Message:
      type: string
    Description:
      type: string
    Duplicates:
      type: array
      items:
        properties:
    Fields:
      type: array
      items:
        properties:
          type: string
    Errors:
      type: array
      items:
        properties:]]></types:shape>
    </types:type>
    <types:type name="Paging" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    total:
      type: integer
    pages:
      type: integer
    pageSize:
      type: integer
    page:
      type: integer]]></types:shape>
    </types:type>
    <types:type name="AuthClient" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: array
items:
  properties:
    client_id:
      type: string
    client_secret:
      type: string]]></types:shape>
    </types:type>
    <types:type name="artemisApiTableRequest" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    database:
      type: string
    objectType:
      type: string
    schema:
      type: string
    objectName:
      type: string
    filters:
      type: string]]></types:shape>
    </types:type>
    <types:type name="artemisApiStoredProcedureRequest" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    database:
      type: string
    objectType:
      type: string
    schema:
      type: string
    objectName:
      type: string
    parameters:
      type: string]]></types:shape>
    </types:type>
    <types:type name="artemisAuthClient" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: array
items:
  properties:
    ClientID:
      type: string
    ClientSecret:
      type: string]]></types:shape>
    </types:type>
    <types:type name="OutboundArrayResponse" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    statusCode:
      type: integer
    reasonPhrase:
      type: string
    response:
      type: array
      items:
        properties:]]></types:shape>
    </types:type>
    <types:type name="OutboundObjectResponse" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    statusCode:
      type: integer
    reasonPhrase:
      type: string
    response:
      type: object]]></types:shape>
    </types:type>
    <types:type name="sfErrorLeadNotConverted" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    Lead:
      type: object
      properties:
        Id:
          type: string
        Name:
          type: string
        Company:
          type: string
        Email:
          type: string
        Status: 
          type: string
        IsConverted:
          type: boolean
    Success:
      type: boolean
    Errors:
      type: array
      items:
        properties:
          Message: 
            type: string]]></types:shape>
    </types:type>
    <types:type name="sfErrorsOpportunityUpsert" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    Opportunity:
      type: object
      properties:
        Id:
          type: string
        Name:
          type: string
    Success:
      type: boolean
    Errors:
      type: array
      items:
        properties:
          StatusCode:
           type: string
          Message:
           type: string
          Duplicates:
            type: array
            items:
              properties:
          Fields:
            type: array
            items:
              properties:
                type: string]]></types:shape>
    </types:type>
    <types:type name="sfErrorsLeadUpsert" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    Lead:
      type: object
      properties:
        Id:
          type: string
        Name:
          type: string
        Company:
          type: string
        Email:
          type: string
    Success:
      type: boolean
    Errors:
      type: array
      items:
        properties:
          StatusCode:
           type: string
          Message:
           type: string
          Duplicates:
            type: array
            items:
              properties:
          Fields:
            type: array
            items:
              properties:
                type: string]]></types:shape>
    </types:type>
    <types:type name="javaLeadRequest" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: string]]></types:shape>
    </types:type>
    <types:type name="salesorceOpportunityRequest" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    counterpartyId:
      type: string
      required: false
    confirmId:
      type: string
      required: false
    isWon:
      type: boolean
      required: false
      default: false
    recordType:
      type: string
      required: false
    leadSource:
      type: string
      required: false
    pricebook:
      type: string
      required: false]]></types:shape>
    </types:type>
    <types:type name="sfAccountConfirmResponse" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: array
items:
  properties:
    OpportunityId:
      type: string
    OpportunityName:
      type: string
    IsWon:
      type: boolean
    ConfirmId:
      type: integer
    AccountId:
      type: string
    AccountName:
      type: string
    CounterpartyId:
      type: integer
    RecordTypeId:
      type: string
    RecordTypeName:
      type: string
    LeadSource:
      type: string
    Pricebook2Id:
      type: string
    Pricebook2Name:
      type: string]]></types:shape>
    </types:type>
    <types:type name="sfOpportunityResponse" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: string]]></types:shape>
    </types:type>
    <types:type name="sfOpportunityResponsePayload" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: array
items:
  properties:
    OpportunityId:
      type: string
    OpportunityName:
      type: string
    IsWon:
      type: boolean
    ConfirmId:
      type: integer
    AccountId:
      type: string
    AccountName:
      type: string
    CounterpartyId:
      type: integer
    RecordTypeId:
      type: string
    RecordTypeName:
      type: string
    LeadSource:
      type: string
    Pricebook2Id:
      type: string
    Pricebook2Name:
      type: string]]></types:shape>
    </types:type>
    <types:type name="CountryRegionResponse" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    Name:
      type: string
    CountryCode:
      type: string
    Region:
      type: string
    CurrencyIsoCode:
      type: string]]></types:shape>
    </types:type>
    <types:type name="sfLeadPayload" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: string]]></types:shape>
    </types:type>
    <types:type name="auto_b377bd6f-ba86-4401-84af-a5f6da156fa6_Input-Payload" format="java">
      <types:shape format="raml" autogeneratedOrigin="5ff84ce8-1c92-4994-b4ad-dac03e6f85c0"><![CDATA[#%RAML 1.0 DataType
type: array
items:
  properties:
    Id:
      type: string
      required: false
    OpportunityId:
      type: string
      required: false
    Product2Id: 
      type: string
      required: true 
    Quantity: 
      type: integer
      required: true  
    UnitPrice: 
      type: number
      multipleOf: 0.01
      required: true
    TotalPrice: 
      type: number
      multipleOf: 0.01
      required: true
    CurrencyIsoCode:
      type: string
      required: true    
    Product_Delivery_Date__c:
      type: date-only
      required: true]]></types:shape>
    </types:type>
    <types:type name="auto_b377bd6f-ba86-4401-84af-a5f6da156fa6_Input-Attributes" format="java">
      <types:shape format="weave" example="weave/autogenerated/b377bd6f-ba86-4401-84af-a5f6da156fa6/Input-Attributes.wev" autogeneratedOrigin="5ff84ce8-1c92-4994-b4ad-dac03e6f85c0"><![CDATA[%dw 2.0

type auto_b377bd6f_ba86_4401_84af_a5f6da156fa6_Input_Attributes = {|
  clientCertificate*?: {|
    publicKey?: {|  |}, 
    "type"?: String, 
    encoded?: Binary
  |}, 
  headers*: {|  |}, 
  listenerPath*: String, 
  localAddress*: String, 
  method*: String, 
  queryParams*: {|
    folderPath: String, 
    fileName: String, 
    entity: String
  |}, 
  queryString*: String, 
  relativePath*: String, 
  remoteAddress*: String, 
  requestPath*: String, 
  requestUri*: String, 
  scheme*: String, 
  uriParams*: {|  |}, 
  version*: String
|}

]]></types:shape>
    </types:type>
    <types:type name="auto_b377bd6f-ba86-4401-84af-a5f6da156fa6_Input-Variables-isLeadConverted" format="java">
      <types:shape format="raml" autogeneratedOrigin="5ff84ce8-1c92-4994-b4ad-dac03e6f85c0"><![CDATA[#%RAML 1.0 DataType
type: boolean]]></types:shape>
    </types:type>
    <types:type name="auto_b377bd6f-ba86-4401-84af-a5f6da156fa6_Input-Variables-outboundHeaders" format="java">
      <types:shape format="weave" example="weave/autogenerated/b377bd6f-ba86-4401-84af-a5f6da156fa6/Input-Variables-outboundHeaders.wev" autogeneratedOrigin="5ff84ce8-1c92-4994-b4ad-dac03e6f85c0"><![CDATA[%dw 2.0

type auto_b377bd6f_ba86_4401_84af_a5f6da156fa6_Input_Variables_outboundHeaders = {|  |}

]]></types:shape>
    </types:type>
    <types:type name="auto_b377bd6f-ba86-4401-84af-a5f6da156fa6_Input-Variables-opportunityId" format="java">
      <types:shape format="raml" autogeneratedOrigin="5ff84ce8-1c92-4994-b4ad-dac03e6f85c0"><![CDATA[#%RAML 1.0 DataType
type: string]]></types:shape>
    </types:type>
    <types:type name="auto_b377bd6f-ba86-4401-84af-a5f6da156fa6_Input-Variables-isCsvFileCreated" format="java">
      <types:shape format="raml" autogeneratedOrigin="5ff84ce8-1c92-4994-b4ad-dac03e6f85c0"><![CDATA[#%RAML 1.0 DataType
type: boolean]]></types:shape>
    </types:type>
    <types:type name="auto_b377bd6f-ba86-4401-84af-a5f6da156fa6_Input-Variables-httpStatus" format="java">
      <types:shape format="raml" autogeneratedOrigin="5ff84ce8-1c92-4994-b4ad-dac03e6f85c0"><![CDATA[#%RAML 1.0 DataType
type: string]]></types:shape>
    </types:type>
    <types:type name="auto_b377bd6f-ba86-4401-84af-a5f6da156fa6_Input-Variables-fileInfo" format="json">
      <types:shape format="weave" example="weave/autogenerated/b377bd6f-ba86-4401-84af-a5f6da156fa6/Input-Variables-fileInfo.wev" autogeneratedOrigin="5ff84ce8-1c92-4994-b4ad-dac03e6f85c0"><![CDATA[%dw 2.0

type auto_b377bd6f_ba86_4401_84af_a5f6da156fa6_Input_Variables_fileInfo = Null

]]></types:shape>
    </types:type>
    <types:type name="auto_b377bd6f-ba86-4401-84af-a5f6da156fa6_Output-Payload" format="java">
      <types:shape format="raml" autogeneratedOrigin="5ff84ce8-1c92-4994-b4ad-dac03e6f85c0"><![CDATA[#%RAML 1.0 DataType
type: array
items:
  properties:
    Id:
      type: string
      required: false
    OpportunityId:
      type: string
      required: false
    Product2Id: 
      type: string
      required: true 
    Quantity: 
      type: integer
      required: true  
    UnitPrice: 
      type: number
      multipleOf: 0.01
      required: true
    TotalPrice: 
      type: number
      multipleOf: 0.01
      required: true
    CurrencyIsoCode:
      type: string
      required: true    
    Product_Delivery_Date__c:
      type: date-only
      required: true]]></types:shape>
    </types:type>
    <types:type name="auto_b377bd6f-ba86-4401-84af-a5f6da156fa6_Output-Attributes" format="java">
      <types:shape format="weave" example="weave/autogenerated/b377bd6f-ba86-4401-84af-a5f6da156fa6/Output-Attributes.wev" autogeneratedOrigin="5ff84ce8-1c92-4994-b4ad-dac03e6f85c0"><![CDATA[%dw 2.0

type auto_b377bd6f_ba86_4401_84af_a5f6da156fa6_Output_Attributes = {|
  clientCertificate*?: {|
    publicKey?: {|  |}, 
    "type"?: String, 
    encoded?: Binary
  |}, 
  headers*: {|  |}, 
  listenerPath*: String, 
  localAddress*: String, 
  method*: String, 
  queryParams*: {|
    folderPath: String, 
    fileName: String, 
    entity: String
  |}, 
  queryString*: String, 
  relativePath*: String, 
  remoteAddress*: String, 
  requestPath*: String, 
  requestUri*: String, 
  scheme*: String, 
  uriParams*: {|  |}, 
  version*: String
|}

]]></types:shape>
    </types:type>
    <types:type name="auto_b377bd6f-ba86-4401-84af-a5f6da156fa6_Output-Variables-sfErrorResponse" format="java">
      <types:shape format="weave" example="weave/autogenerated/b377bd6f-ba86-4401-84af-a5f6da156fa6/Output-Variables-sfErrorResponse.wev" autogeneratedOrigin="5ff84ce8-1c92-4994-b4ad-dac03e6f85c0"><![CDATA[%dw 2.0

type auto_b377bd6f_ba86_4401_84af_a5f6da156fa6_Output_Variables_sfErrorResponse = Null

]]></types:shape>
    </types:type>
    <types:type name="auto_b377bd6f-ba86-4401-84af-a5f6da156fa6_Output-Variables-sfOpportunityLineItemsResponse" format="java">
      <types:shape format="weave" example="weave/autogenerated/b377bd6f-ba86-4401-84af-a5f6da156fa6/Output-Variables-sfOpportunityLineItemsResponse.wev" autogeneratedOrigin="5ff84ce8-1c92-4994-b4ad-dac03e6f85c0"><![CDATA[%dw 2.0

type auto_b377bd6f_ba86_4401_84af_a5f6da156fa6_Output_Variables_sfOpportunityLineItemsResponse = Array<org_mule_runtime_api_message_Message {"typeId": "org.mule.runtime.api.message.Message"}>
type org_mule_runtime_api_message_Message = {|
  payload: OpportunityLineItem {"typeId": "OpportunityLineItem",
  "label": "Opportunity Product"}, 
  attributes: Null
|} {"typeId": "org.mule.runtime.api.message.Message"}
type OpportunityLineItem = {|
  Id?: String {"typeId": "Id"}, 
  OpportunityId?: String, 
  PricebookEntry?: Array<{| Vintage__c?: String {"typeId": "Vintage__c"} |}>, 
  PricebookEntryId?: String, 
  Product2Id?: String, 
  Product_Delivery_Date__c?: Date {"typeId": "Product_Delivery_Date__c"}, 
  Quantity?: Number {"typeId": "Quantity"}, 
  UnitPrice?: Number {"typeId": "UnitPrice"}
|} {"typeId": "OpportunityLineItem",
"label": "Opportunity Product"}





]]></types:shape>
    </types:type>
    <types:type name="auto_b377bd6f-ba86-4401-84af-a5f6da156fa6_Output-Variables-isLeadConverted" format="java">
      <types:shape format="raml" autogeneratedOrigin="5ff84ce8-1c92-4994-b4ad-dac03e6f85c0"><![CDATA[#%RAML 1.0 DataType
type: boolean]]></types:shape>
    </types:type>
    <types:type name="auto_b377bd6f-ba86-4401-84af-a5f6da156fa6_Output-Variables-outboundHeaders" format="java">
      <types:shape format="weave" example="weave/autogenerated/b377bd6f-ba86-4401-84af-a5f6da156fa6/Output-Variables-outboundHeaders.wev" autogeneratedOrigin="5ff84ce8-1c92-4994-b4ad-dac03e6f85c0"><![CDATA[%dw 2.0

type auto_b377bd6f_ba86_4401_84af_a5f6da156fa6_Output_Variables_outboundHeaders = {|  |}

]]></types:shape>
    </types:type>
    <types:type name="auto_b377bd6f-ba86-4401-84af-a5f6da156fa6_Output-Variables-opportunityId" format="java">
      <types:shape format="raml" autogeneratedOrigin="5ff84ce8-1c92-4994-b4ad-dac03e6f85c0"><![CDATA[#%RAML 1.0 DataType
type: string]]></types:shape>
    </types:type>
    <types:type name="auto_b377bd6f-ba86-4401-84af-a5f6da156fa6_Output-Variables-isCsvFileCreated" format="java">
      <types:shape format="raml" autogeneratedOrigin="5ff84ce8-1c92-4994-b4ad-dac03e6f85c0"><![CDATA[#%RAML 1.0 DataType
type: boolean]]></types:shape>
    </types:type>
    <types:type name="auto_b377bd6f-ba86-4401-84af-a5f6da156fa6_Output-Variables-httpStatus" format="java">
      <types:shape format="raml" autogeneratedOrigin="5ff84ce8-1c92-4994-b4ad-dac03e6f85c0"><![CDATA[#%RAML 1.0 DataType
type: string]]></types:shape>
    </types:type>
    <types:type name="auto_b377bd6f-ba86-4401-84af-a5f6da156fa6_Output-Variables-fileInfo" format="json">
      <types:shape format="weave" example="weave/autogenerated/b377bd6f-ba86-4401-84af-a5f6da156fa6/Output-Variables-fileInfo.wev" autogeneratedOrigin="5ff84ce8-1c92-4994-b4ad-dac03e6f85c0"><![CDATA[%dw 2.0

type auto_b377bd6f_ba86_4401_84af_a5f6da156fa6_Output_Variables_fileInfo = Null

]]></types:shape>
    </types:type>
    <types:type name="auto_5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c_Input-Payload" format="java">
      <types:shape format="weave" example="weave/autogenerated/5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c/Input-Payload.wev" autogeneratedOrigin="40730268-7635-492f-82b2-8435579962ec"><![CDATA[%dw 2.0

type auto_5f229e29_b1ef_4c30_b5d4_dd1afa6bd34c_Input_Payload = Array<{|  Id?: String,   OpportunityId?: String,   Product2Id: String,   Quantity: Number,   UnitPrice: Number,   TotalPrice: Number,   CurrencyIsoCode: String,   Product_Delivery_Date__c: Date|}> {"typeAlias": "auto_5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c_Input-Payload"}

]]></types:shape>
    </types:type>
    <types:type name="auto_5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c_Input-Attributes" format="java">
      <types:shape format="weave" example="weave/autogenerated/5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c/Input-Attributes.wev" autogeneratedOrigin="40730268-7635-492f-82b2-8435579962ec"><![CDATA[%dw 2.0

type auto_5f229e29_b1ef_4c30_b5d4_dd1afa6bd34c_Input_Attributes = {|
  clientCertificate*?: {|
    publicKey?: {|  |}, 
    "type"?: String, 
    encoded?: Binary
  |}, 
  headers*: {|  |}, 
  listenerPath*: String, 
  localAddress*: String, 
  method*: String, 
  queryParams*: {|
    folderPath: String, 
    fileName: String, 
    entity: String
  |}, 
  queryString*: String, 
  relativePath*: String, 
  remoteAddress*: String, 
  requestPath*: String, 
  requestUri*: String, 
  scheme*: String, 
  uriParams*: {|  |}, 
  version*: String
|}

]]></types:shape>
    </types:type>
    <types:type name="auto_5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c_Input-Variables-outboundHeaders" format="java">
      <types:shape format="weave" example="weave/autogenerated/5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c/Input-Variables-outboundHeaders.wev" autogeneratedOrigin="40730268-7635-492f-82b2-8435579962ec"><![CDATA[%dw 2.0

type auto_5f229e29_b1ef_4c30_b5d4_dd1afa6bd34c_Input_Variables_outboundHeaders = {|  |}

]]></types:shape>
    </types:type>
    <types:type name="auto_5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c_Input-Variables-isCsvFileCreated" format="java">
      <types:shape format="raml" autogeneratedOrigin="40730268-7635-492f-82b2-8435579962ec"><![CDATA[#%RAML 1.0 DataType
type: boolean]]></types:shape>
    </types:type>
    <types:type name="auto_5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c_Input-Variables-sfOpportunityLineItemsResponse" format="java">
      <types:shape format="weave" example="weave/autogenerated/5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c/Input-Variables-sfOpportunityLineItemsResponse.wev" autogeneratedOrigin="40730268-7635-492f-82b2-8435579962ec"><![CDATA[%dw 2.0

type auto_5f229e29_b1ef_4c30_b5d4_dd1afa6bd34c_Input_Variables_sfOpportunityLineItemsResponse = Array<org_mule_runtime_api_message_Message {"typeId": "org.mule.runtime.api.message.Message"}>
type org_mule_runtime_api_message_Message = {|
  payload: OpportunityLineItem {"typeId": "OpportunityLineItem",
  "label": "Opportunity Product"}, 
  attributes: Null
|} {"typeId": "org.mule.runtime.api.message.Message"}
type OpportunityLineItem = {|
  Id?: String {"typeId": "Id"}, 
  OpportunityId?: String, 
  PricebookEntry?: Array<{| Vintage__c?: String {"typeId": "Vintage__c"} |}>, 
  PricebookEntryId?: String, 
  Product2Id?: String, 
  Product_Delivery_Date__c?: Date {"typeId": "Product_Delivery_Date__c"}, 
  Quantity?: Number {"typeId": "Quantity"}, 
  UnitPrice?: Number {"typeId": "UnitPrice"}
|} {"typeId": "OpportunityLineItem",
"label": "Opportunity Product"}





]]></types:shape>
    </types:type>
    <types:type name="auto_5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c_Input-Variables-fileInfo" format="json">
      <types:shape format="weave" example="weave/autogenerated/5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c/Input-Variables-fileInfo.wev" autogeneratedOrigin="40730268-7635-492f-82b2-8435579962ec"><![CDATA[%dw 2.0

type auto_5f229e29_b1ef_4c30_b5d4_dd1afa6bd34c_Input_Variables_fileInfo = Null

]]></types:shape>
    </types:type>
    <types:type name="auto_5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c_Output-Payload" format="java">
      <types:shape format="raml" autogeneratedOrigin="40730268-7635-492f-82b2-8435579962ec"><![CDATA[#%RAML 1.0 DataType
type: array
items:
  properties:
    Id:
      type: string
      required: false
    OpportunityId:
      type: string
      required: false
    Product2Id: 
      type: string
      required: true 
    Quantity: 
      type: integer
      required: true  
    UnitPrice: 
      type: number
      multipleOf: 0.01
      required: true
    TotalPrice: 
      type: number
      multipleOf: 0.01
      required: true
    CurrencyIsoCode:
      type: string
      required: true    
    Product_Delivery_Date__c:
      type: date-only
      required: true]]></types:shape>
    </types:type>
    <types:type name="auto_5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c_Output-Attributes" format="java">
      <types:shape format="weave" example="weave/autogenerated/5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c/Output-Attributes.wev" autogeneratedOrigin="40730268-7635-492f-82b2-8435579962ec"><![CDATA[%dw 2.0

type auto_5f229e29_b1ef_4c30_b5d4_dd1afa6bd34c_Output_Attributes = {|
  clientCertificate*?: {|
    publicKey?: {|  |}, 
    "type"?: String, 
    encoded?: Binary
  |}, 
  headers*: {|  |}, 
  listenerPath*: String, 
  localAddress*: String, 
  method*: String, 
  queryParams*: {|
    folderPath: String, 
    fileName: String, 
    entity: String
  |}, 
  queryString*: String, 
  relativePath*: String, 
  remoteAddress*: String, 
  requestPath*: String, 
  requestUri*: String, 
  scheme*: String, 
  uriParams*: {|  |}, 
  version*: String
|}

]]></types:shape>
    </types:type>
    <types:type name="auto_5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c_Output-Variables-sfPricebookEntryResponse" format="java">
      <types:shape format="weave" example="weave/autogenerated/5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c/Output-Variables-sfPricebookEntryResponse.wev" autogeneratedOrigin="40730268-7635-492f-82b2-8435579962ec"><![CDATA[%dw 2.0

type auto_5f229e29_b1ef_4c30_b5d4_dd1afa6bd34c_Output_Variables_sfPricebookEntryResponse = Array<{|  attributes*: Null,   payload*: PricebookEntry {"label": "Price Book Entry",
  "typeId": "PricebookEntry"}|}>
type PricebookEntry = {|
  Id?: String {"typeId": "Id"}, 
  Product2?: Array<{|    Climate_Action_Portal_Product_ID__c?: String {"typeId": "Climate_Action_Portal_Product_ID__c"},     Name?: String {"typeId": "Name"},     ProductCode?: String {"typeId": "ProductCode"}  |}>, 
  Product2Id?: String, 
  Vintage__c?: String {"typeId": "Vintage__c"}
|} {"label": "Price Book Entry",
"typeId": "PricebookEntry"}



]]></types:shape>
    </types:type>
    <types:type name="auto_5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c_Output-Variables-pricebookEntry" format="java">
      <types:shape format="weave" example="weave/autogenerated/5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c/Output-Variables-pricebookEntry.wev" autogeneratedOrigin="40730268-7635-492f-82b2-8435579962ec"><![CDATA[%dw 2.0

type auto_5f229e29_b1ef_4c30_b5d4_dd1afa6bd34c_Output_Variables_pricebookEntry = Null

]]></types:shape>
    </types:type>
    <types:type name="auto_5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c_Output-Variables-sfOpportunityLineItemsResponse" format="java">
      <types:shape format="weave" example="weave/autogenerated/5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c/Output-Variables-sfOpportunityLineItemsResponse.wev" autogeneratedOrigin="40730268-7635-492f-82b2-8435579962ec"><![CDATA[%dw 2.0

type auto_5f229e29_b1ef_4c30_b5d4_dd1afa6bd34c_Output_Variables_sfOpportunityLineItemsResponse = Array<org_mule_runtime_api_message_Message {"typeId": "org.mule.runtime.api.message.Message"}>
type org_mule_runtime_api_message_Message = {|
  payload: OpportunityLineItem {"typeId": "OpportunityLineItem",
  "label": "Opportunity Product"}, 
  attributes: Null
|} {"typeId": "org.mule.runtime.api.message.Message"}
type OpportunityLineItem = {|
  Id?: String {"typeId": "Id"}, 
  OpportunityId?: String, 
  PricebookEntry?: Array<{| Vintage__c?: String {"typeId": "Vintage__c"} |}>, 
  PricebookEntryId?: String, 
  Product2Id?: String, 
  Product_Delivery_Date__c?: Date {"typeId": "Product_Delivery_Date__c"}, 
  Quantity?: Number {"typeId": "Quantity"}, 
  UnitPrice?: Number {"typeId": "UnitPrice"}
|} {"typeId": "OpportunityLineItem",
"label": "Opportunity Product"}





]]></types:shape>
    </types:type>
    <types:type name="auto_5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c_Output-Variables-fileInfo" format="json">
      <types:shape format="weave" example="weave/autogenerated/5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c/Output-Variables-fileInfo.wev" autogeneratedOrigin="40730268-7635-492f-82b2-8435579962ec"><![CDATA[%dw 2.0

type auto_5f229e29_b1ef_4c30_b5d4_dd1afa6bd34c_Output_Variables_fileInfo = Null

]]></types:shape>
    </types:type>
    <types:type name="auto_5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c_Output-Variables-pricebookEntryResponse" format="java">
      <types:shape format="raml" autogeneratedOrigin="40730268-7635-492f-82b2-8435579962ec"><![CDATA[#%RAML 1.0 DataType
type: array
items:
  properties:
    Id:
      type: string
      required: false
    OpportunityId:
      type: string
      required: false
    Product2Id: 
      type: string
      required: true 
    Quantity: 
      type: integer
      required: true  
    UnitPrice: 
      type: number
      multipleOf: 0.01
      required: true
    TotalPrice: 
      type: number
      multipleOf: 0.01
      required: true
    CurrencyIsoCode:
      type: string
      required: true    
    Product_Delivery_Date__c:
      type: date-only
      required: true]]></types:shape>
    </types:type>
    <types:type name="auto_413132fc-fc5e-42d8-8f75-babfb2523476_Input-Payload" format="java">
      <types:shape format="weave" example="weave/autogenerated/413132fc-fc5e-42d8-8f75-babfb2523476/Input-Payload.wev" autogeneratedOrigin="c5a28574-ac4c-4765-8f81-41b531d3c502"><![CDATA[%dw 2.0

type auto_413132fc_fc5e_42d8_8f75_babfb2523476_Input_Payload = Any

]]></types:shape>
    </types:type>
    <types:type name="auto_413132fc-fc5e-42d8-8f75-babfb2523476_Input-Attributes" format="java">
      <types:shape format="weave" example="weave/autogenerated/413132fc-fc5e-42d8-8f75-babfb2523476/Input-Attributes.wev" autogeneratedOrigin="c5a28574-ac4c-4765-8f81-41b531d3c502"><![CDATA[%dw 2.0

type auto_413132fc_fc5e_42d8_8f75_babfb2523476_Input_Attributes = {|
  clientCertificate*?: {|
    publicKey?: {|  |}, 
    "type"?: String, 
    encoded?: Binary
  |}, 
  headers*: {|  |}, 
  listenerPath*: String, 
  localAddress*: String, 
  method*: String, 
  queryParams*: {|
    folderPath: String, 
    fileName: String, 
    entity: String
  |}, 
  queryString*: String, 
  relativePath*: String, 
  remoteAddress*: String, 
  requestPath*: String, 
  requestUri*: String, 
  scheme*: String, 
  uriParams*: {|  |}, 
  version*: String
|}

]]></types:shape>
    </types:type>
    <types:type name="auto_413132fc-fc5e-42d8-8f75-babfb2523476_Input-Variables-sfErrorResponse" format="java">
      <types:shape format="weave" example="weave/autogenerated/413132fc-fc5e-42d8-8f75-babfb2523476/Input-Variables-sfErrorResponse.wev" autogeneratedOrigin="c5a28574-ac4c-4765-8f81-41b531d3c502"><![CDATA[%dw 2.0

type auto_413132fc_fc5e_42d8_8f75_babfb2523476_Input_Variables_sfErrorResponse = Any

]]></types:shape>
    </types:type>
    <types:type name="auto_413132fc-fc5e-42d8-8f75-babfb2523476_Input-Variables-sfOpportunityResponse" format="java">
      <types:shape format="weave" example="weave/autogenerated/413132fc-fc5e-42d8-8f75-babfb2523476/Input-Variables-sfOpportunityResponse.wev" autogeneratedOrigin="c5a28574-ac4c-4765-8f81-41b531d3c502"><![CDATA[%dw 2.0

type auto_413132fc_fc5e_42d8_8f75_babfb2523476_Input_Variables_sfOpportunityResponse = org_mule_runtime_api_bulk_BulkOperationResult {"typeAlias": "BulkOperationResult",
"typeId": "org.mule.runtime.api.bulk.BulkOperationResult"} | Null
type org_mule_runtime_api_bulk_BulkOperationResult = {|
  id?: java_io_Serializable {"typeId": "java.io.Serializable",
  "typeAlias": "Serializable"}, 
  items?: Array<org_mule_runtime_api_bulk_BulkItem {"typeAlias": "BulkItem",
  "typeId": "org.mule.runtime.api.bulk.BulkItem"}>, 
  successful?: Boolean {"typeId": "boolean"}
|} {"typeAlias": "BulkOperationResult",
"typeId": "org.mule.runtime.api.bulk.BulkOperationResult"}
type org_mule_runtime_api_bulk_BulkItem = {|
  exception?: java_lang_Exception {"typeId": "java.lang.Exception",
  "typeAlias": "Exception",
  "class": "java.lang.Exception"}, 
  id?: java_io_Serializable_38 {"typeId": "java.io.Serializable",
  "typeAlias": "Serializable"}, 
  message?: String, 
  payload?: org_mule_extension_salesforce_api_core_UpsertResult {"typeId": "org.mule.extension.salesforce.api.core.UpsertResult",
  "class": "org.mule.extension.salesforce.api.core.UpsertResult",
  "typeAlias": "UpsertResult"}, 
  statusCode?: String, 
  successful?: Boolean {"typeId": "boolean"}
|} {"typeAlias": "BulkItem",
"typeId": "org.mule.runtime.api.bulk.BulkItem"}
type org_mule_extension_salesforce_api_core_UpsertResult = {|
  created?: Boolean {"typeId": "boolean"}, 
  errors?: Array<org_mule_extension_salesforce_api_core_Error {"class": "org.mule.extension.salesforce.api.core.Error",
  "typeAlias": "Error",
  "typeId": "org.mule.extension.salesforce.api.core.Error"}>, 
  id?: String, 
  success?: Boolean {"typeId": "boolean"}
|} {"typeId": "org.mule.extension.salesforce.api.core.UpsertResult",
"class": "org.mule.extension.salesforce.api.core.UpsertResult",
"typeAlias": "UpsertResult"}
type org_mule_extension_salesforce_api_core_Error = {|
  duplicateResult?: org_mule_extension_salesforce_api_core_DuplicateResult {"typeAlias": "DuplicateResult",
  "class": "org.mule.extension.salesforce.api.core.DuplicateResult",
  "typeId": "org.mule.extension.salesforce.api.core.DuplicateResult"}, 
  fields?: Array<String>, 
  message?: String, 
  statusCode?: String
|} {"class": "org.mule.extension.salesforce.api.core.Error",
"typeAlias": "Error",
"typeId": "org.mule.extension.salesforce.api.core.Error"}
type org_mule_extension_salesforce_api_core_DuplicateResult = {|
  allowSave?: Boolean {"typeId": "boolean"}, 
  duplicateRule?: String, 
  duplicateRuleEntityType?: String, 
  errorMessage?: String, 
  matchResults?: Array<org_mule_extension_salesforce_api_core_MatchResult {"typeId": "org.mule.extension.salesforce.api.core.MatchResult",
  "typeAlias": "MatchResult",
  "class": "org.mule.extension.salesforce.api.core.MatchResult"}>
|} {"typeAlias": "DuplicateResult",
"class": "org.mule.extension.salesforce.api.core.DuplicateResult",
"typeId": "org.mule.extension.salesforce.api.core.DuplicateResult"}
type org_mule_extension_salesforce_api_core_MatchResult = {|
  entityType?: String, 
  errors?: Array<org_mule_extension_salesforce_api_core_SimpleError {"class": "org.mule.extension.salesforce.api.core.SimpleError",
  "typeAlias": "SimpleError",
  "typeId": "org.mule.extension.salesforce.api.core.SimpleError"}>, 
  matchEngine?: String, 
  matchRecords?: Array<org_mule_extension_salesforce_api_utility_MatchRecord {"typeAlias": "MatchRecord",
  "class": "org.mule.extension.salesforce.api.utility.MatchRecord",
  "typeId": "org.mule.extension.salesforce.api.utility.MatchRecord"}>, 
  rule?: String, 
  size?: Number, 
  success?: Boolean {"typeId": "boolean"}
|} {"typeId": "org.mule.extension.salesforce.api.core.MatchResult",
"typeAlias": "MatchResult",
"class": "org.mule.extension.salesforce.api.core.MatchResult"}
type org_mule_extension_salesforce_api_core_SimpleError = {|
  fields?: Array<String>, 
  message?: String, 
  statusCode?: String
|} {"class": "org.mule.extension.salesforce.api.core.SimpleError",
"typeAlias": "SimpleError",
"typeId": "org.mule.extension.salesforce.api.core.SimpleError"}


type org_mule_extension_salesforce_api_utility_MatchRecord = {|
  additionalInformation?: Array<org_mule_extension_salesforce_api_utility_AdditionalInformationMap {"typeAlias": "AdditionalInformationMap",
  "typeId": "org.mule.extension.salesforce.api.utility.AdditionalInformationMap",
  "class": "org.mule.extension.salesforce.api.utility.AdditionalInformationMap"}>, 
  fieldDiffs?: Array<org_mule_extension_salesforce_api_utility_FieldDiff {"class": "org.mule.extension.salesforce.api.utility.FieldDiff",
  "typeAlias": "FieldDiff",
  "typeId": "org.mule.extension.salesforce.api.utility.FieldDiff"}>, 
  matchConfidence?: Number, 
  record?: { _?: java_lang_Object {"class": "java.lang.Object",
    "typeAlias": "Object",
    "typeId": "java.lang.Object"} }
|} {"typeAlias": "MatchRecord",
"class": "org.mule.extension.salesforce.api.utility.MatchRecord",
"typeId": "org.mule.extension.salesforce.api.utility.MatchRecord"}
type org_mule_extension_salesforce_api_utility_FieldDiff = {|
  difference?: ("DIFFERENT" | "NULL" | "SAME" | "SIMILAR") {"typeId": "org.mule.extension.salesforce.api.utility.DifferenceType"}, 
  name?: String
|} {"class": "org.mule.extension.salesforce.api.utility.FieldDiff",
"typeAlias": "FieldDiff",
"typeId": "org.mule.extension.salesforce.api.utility.FieldDiff"}


type org_mule_extension_salesforce_api_utility_AdditionalInformationMap = {|
  name?: String, 
  value?: String
|} {"typeAlias": "AdditionalInformationMap",
"typeId": "org.mule.extension.salesforce.api.utility.AdditionalInformationMap",
"class": "org.mule.extension.salesforce.api.utility.AdditionalInformationMap"}


type java_lang_Object = {|  |} {"class": "java.lang.Object",
"typeAlias": "Object",
"typeId": "java.lang.Object"}












type java_io_Serializable_38 = {|  |} {"typeId": "java.io.Serializable",
"typeAlias": "Serializable"}


type java_lang_Exception = {|
  cause?: java_lang_Throwable {"class": "java.lang.Throwable",
  "typeId": "java.lang.Throwable",
  "typeAlias": "Throwable"}, 
  localizedMessage?: String, 
  message?: String, 
  stackTrace?: Array<java_lang_StackTraceElement_15 {"typeAlias": "StackTraceElement",
  "typeId": "java.lang.StackTraceElement"}>, 
  suppressed?: Array<java_lang_Throwable_25 {"class": "java.lang.Throwable",
  "typeId": "java.lang.Throwable",
  "typeAlias": "Throwable"}>
|} {"typeId": "java.lang.Exception",
"typeAlias": "Exception",
"class": "java.lang.Exception"}
type java_lang_Throwable_25 = {|
  cause?: java_lang_Throwable_25 {"class": "java.lang.Throwable",
  "typeId": "java.lang.Throwable",
  "typeAlias": "Throwable"}, 
  localizedMessage?: String, 
  message?: String, 
  stackTrace?: Array<java_lang_StackTraceElement_29 {"typeAlias": "StackTraceElement",
  "typeId": "java.lang.StackTraceElement"}>, 
  suppressed?: Array<java_lang_Throwable_25 {"class": "java.lang.Throwable",
  "typeId": "java.lang.Throwable",
  "typeAlias": "Throwable"}>
|} {"class": "java.lang.Throwable",
"typeId": "java.lang.Throwable",
"typeAlias": "Throwable"}
type java_lang_StackTraceElement_29 = {|
  classLoaderName?: String, 
  className?: String, 
  fileName?: String, 
  lineNumber?: Number, 
  methodName?: String, 
  moduleName?: String, 
  moduleVersion?: String, 
  nativeMethod?: Boolean {"typeId": "boolean"}
|} {"typeAlias": "StackTraceElement",
"typeId": "java.lang.StackTraceElement"}




type java_lang_StackTraceElement_15 = {|
  classLoaderName?: String, 
  className?: String, 
  fileName?: String, 
  lineNumber?: Number, 
  methodName?: String, 
  moduleName?: String, 
  moduleVersion?: String, 
  nativeMethod?: Boolean {"typeId": "boolean"}
|} {"typeAlias": "StackTraceElement",
"typeId": "java.lang.StackTraceElement"}


type java_lang_Throwable = {|
  cause?: java_lang_Throwable {"class": "java.lang.Throwable",
  "typeId": "java.lang.Throwable",
  "typeAlias": "Throwable"}, 
  localizedMessage?: String, 
  message?: String, 
  stackTrace?: Array<java_lang_StackTraceElement {"typeAlias": "StackTraceElement",
  "typeId": "java.lang.StackTraceElement"}>, 
  suppressed?: Array<java_lang_Throwable {"class": "java.lang.Throwable",
  "typeId": "java.lang.Throwable",
  "typeAlias": "Throwable"}>
|} {"class": "java.lang.Throwable",
"typeId": "java.lang.Throwable",
"typeAlias": "Throwable"}
type java_lang_StackTraceElement = {|
  classLoaderName?: String, 
  className?: String, 
  fileName?: String, 
  lineNumber?: Number, 
  methodName?: String, 
  moduleName?: String, 
  moduleVersion?: String, 
  nativeMethod?: Boolean {"typeId": "boolean"}
|} {"typeAlias": "StackTraceElement",
"typeId": "java.lang.StackTraceElement"}








type java_io_Serializable = {|  |} {"typeId": "java.io.Serializable",
"typeAlias": "Serializable"}





]]></types:shape>
    </types:type>
    <types:type name="auto_413132fc-fc5e-42d8-8f75-babfb2523476_Input-Variables-httpStatus" format="java">
      <types:shape format="raml" autogeneratedOrigin="c5a28574-ac4c-4765-8f81-41b531d3c502"><![CDATA[#%RAML 1.0 DataType
type: string]]></types:shape>
    </types:type>
    <types:type name="auto_413132fc-fc5e-42d8-8f75-babfb2523476_Input-Variables-sfAccountResponse" format="java">
      <types:shape format="weave" example="weave/autogenerated/413132fc-fc5e-42d8-8f75-babfb2523476/Input-Variables-sfAccountResponse.wev" autogeneratedOrigin="c5a28574-ac4c-4765-8f81-41b531d3c502"><![CDATA[%dw 2.0

type auto_413132fc_fc5e_42d8_8f75_babfb2523476_Input_Variables_sfAccountResponse = org_mule_runtime_api_bulk_BulkOperationResult {"typeAlias": "BulkOperationResult",
"typeId": "org.mule.runtime.api.bulk.BulkOperationResult"} | Null
type org_mule_runtime_api_bulk_BulkOperationResult = {|
  id?: java_io_Serializable {"typeId": "java.io.Serializable",
  "typeAlias": "Serializable"}, 
  items?: Array<org_mule_runtime_api_bulk_BulkItem {"typeAlias": "BulkItem",
  "typeId": "org.mule.runtime.api.bulk.BulkItem"}>, 
  successful?: Boolean {"typeId": "boolean"}
|} {"typeAlias": "BulkOperationResult",
"typeId": "org.mule.runtime.api.bulk.BulkOperationResult"}
type org_mule_runtime_api_bulk_BulkItem = {|
  exception?: java_lang_Exception {"typeId": "java.lang.Exception",
  "typeAlias": "Exception",
  "class": "java.lang.Exception"}, 
  id?: java_io_Serializable_38 {"typeId": "java.io.Serializable",
  "typeAlias": "Serializable"}, 
  message?: String, 
  payload?: org_mule_extension_salesforce_api_core_UpsertResult {"typeId": "org.mule.extension.salesforce.api.core.UpsertResult",
  "class": "org.mule.extension.salesforce.api.core.UpsertResult",
  "typeAlias": "UpsertResult"}, 
  statusCode?: String, 
  successful?: Boolean {"typeId": "boolean"}
|} {"typeAlias": "BulkItem",
"typeId": "org.mule.runtime.api.bulk.BulkItem"}
type org_mule_extension_salesforce_api_core_UpsertResult = {|
  created?: Boolean {"typeId": "boolean"}, 
  errors?: Array<org_mule_extension_salesforce_api_core_Error {"class": "org.mule.extension.salesforce.api.core.Error",
  "typeAlias": "Error",
  "typeId": "org.mule.extension.salesforce.api.core.Error"}>, 
  id?: String, 
  success?: Boolean {"typeId": "boolean"}
|} {"typeId": "org.mule.extension.salesforce.api.core.UpsertResult",
"class": "org.mule.extension.salesforce.api.core.UpsertResult",
"typeAlias": "UpsertResult"}
type org_mule_extension_salesforce_api_core_Error = {|
  duplicateResult?: org_mule_extension_salesforce_api_core_DuplicateResult {"typeAlias": "DuplicateResult",
  "class": "org.mule.extension.salesforce.api.core.DuplicateResult",
  "typeId": "org.mule.extension.salesforce.api.core.DuplicateResult"}, 
  fields?: Array<String>, 
  message?: String, 
  statusCode?: String
|} {"class": "org.mule.extension.salesforce.api.core.Error",
"typeAlias": "Error",
"typeId": "org.mule.extension.salesforce.api.core.Error"}
type org_mule_extension_salesforce_api_core_DuplicateResult = {|
  allowSave?: Boolean {"typeId": "boolean"}, 
  duplicateRule?: String, 
  duplicateRuleEntityType?: String, 
  errorMessage?: String, 
  matchResults?: Array<org_mule_extension_salesforce_api_core_MatchResult {"typeId": "org.mule.extension.salesforce.api.core.MatchResult",
  "typeAlias": "MatchResult",
  "class": "org.mule.extension.salesforce.api.core.MatchResult"}>
|} {"typeAlias": "DuplicateResult",
"class": "org.mule.extension.salesforce.api.core.DuplicateResult",
"typeId": "org.mule.extension.salesforce.api.core.DuplicateResult"}
type org_mule_extension_salesforce_api_core_MatchResult = {|
  entityType?: String, 
  errors?: Array<org_mule_extension_salesforce_api_core_SimpleError {"class": "org.mule.extension.salesforce.api.core.SimpleError",
  "typeAlias": "SimpleError",
  "typeId": "org.mule.extension.salesforce.api.core.SimpleError"}>, 
  matchEngine?: String, 
  matchRecords?: Array<org_mule_extension_salesforce_api_utility_MatchRecord {"typeAlias": "MatchRecord",
  "class": "org.mule.extension.salesforce.api.utility.MatchRecord",
  "typeId": "org.mule.extension.salesforce.api.utility.MatchRecord"}>, 
  rule?: String, 
  size?: Number, 
  success?: Boolean {"typeId": "boolean"}
|} {"typeId": "org.mule.extension.salesforce.api.core.MatchResult",
"typeAlias": "MatchResult",
"class": "org.mule.extension.salesforce.api.core.MatchResult"}
type org_mule_extension_salesforce_api_core_SimpleError = {|
  fields?: Array<String>, 
  message?: String, 
  statusCode?: String
|} {"class": "org.mule.extension.salesforce.api.core.SimpleError",
"typeAlias": "SimpleError",
"typeId": "org.mule.extension.salesforce.api.core.SimpleError"}


type org_mule_extension_salesforce_api_utility_MatchRecord = {|
  additionalInformation?: Array<org_mule_extension_salesforce_api_utility_AdditionalInformationMap {"typeAlias": "AdditionalInformationMap",
  "typeId": "org.mule.extension.salesforce.api.utility.AdditionalInformationMap",
  "class": "org.mule.extension.salesforce.api.utility.AdditionalInformationMap"}>, 
  fieldDiffs?: Array<org_mule_extension_salesforce_api_utility_FieldDiff {"class": "org.mule.extension.salesforce.api.utility.FieldDiff",
  "typeAlias": "FieldDiff",
  "typeId": "org.mule.extension.salesforce.api.utility.FieldDiff"}>, 
  matchConfidence?: Number, 
  record?: { _?: java_lang_Object {"class": "java.lang.Object",
    "typeAlias": "Object",
    "typeId": "java.lang.Object"} }
|} {"typeAlias": "MatchRecord",
"class": "org.mule.extension.salesforce.api.utility.MatchRecord",
"typeId": "org.mule.extension.salesforce.api.utility.MatchRecord"}
type org_mule_extension_salesforce_api_utility_FieldDiff = {|
  difference?: ("DIFFERENT" | "NULL" | "SAME" | "SIMILAR") {"typeId": "org.mule.extension.salesforce.api.utility.DifferenceType"}, 
  name?: String
|} {"class": "org.mule.extension.salesforce.api.utility.FieldDiff",
"typeAlias": "FieldDiff",
"typeId": "org.mule.extension.salesforce.api.utility.FieldDiff"}


type org_mule_extension_salesforce_api_utility_AdditionalInformationMap = {|
  name?: String, 
  value?: String
|} {"typeAlias": "AdditionalInformationMap",
"typeId": "org.mule.extension.salesforce.api.utility.AdditionalInformationMap",
"class": "org.mule.extension.salesforce.api.utility.AdditionalInformationMap"}


type java_lang_Object = {|  |} {"class": "java.lang.Object",
"typeAlias": "Object",
"typeId": "java.lang.Object"}












type java_io_Serializable_38 = {|  |} {"typeId": "java.io.Serializable",
"typeAlias": "Serializable"}


type java_lang_Exception = {|
  cause?: java_lang_Throwable {"class": "java.lang.Throwable",
  "typeId": "java.lang.Throwable",
  "typeAlias": "Throwable"}, 
  localizedMessage?: String, 
  message?: String, 
  stackTrace?: Array<java_lang_StackTraceElement_15 {"typeAlias": "StackTraceElement",
  "typeId": "java.lang.StackTraceElement"}>, 
  suppressed?: Array<java_lang_Throwable_25 {"class": "java.lang.Throwable",
  "typeId": "java.lang.Throwable",
  "typeAlias": "Throwable"}>
|} {"typeId": "java.lang.Exception",
"typeAlias": "Exception",
"class": "java.lang.Exception"}
type java_lang_Throwable_25 = {|
  cause?: java_lang_Throwable_25 {"class": "java.lang.Throwable",
  "typeId": "java.lang.Throwable",
  "typeAlias": "Throwable"}, 
  localizedMessage?: String, 
  message?: String, 
  stackTrace?: Array<java_lang_StackTraceElement_29 {"typeAlias": "StackTraceElement",
  "typeId": "java.lang.StackTraceElement"}>, 
  suppressed?: Array<java_lang_Throwable_25 {"class": "java.lang.Throwable",
  "typeId": "java.lang.Throwable",
  "typeAlias": "Throwable"}>
|} {"class": "java.lang.Throwable",
"typeId": "java.lang.Throwable",
"typeAlias": "Throwable"}
type java_lang_StackTraceElement_29 = {|
  classLoaderName?: String, 
  className?: String, 
  fileName?: String, 
  lineNumber?: Number, 
  methodName?: String, 
  moduleName?: String, 
  moduleVersion?: String, 
  nativeMethod?: Boolean {"typeId": "boolean"}
|} {"typeAlias": "StackTraceElement",
"typeId": "java.lang.StackTraceElement"}




type java_lang_StackTraceElement_15 = {|
  classLoaderName?: String, 
  className?: String, 
  fileName?: String, 
  lineNumber?: Number, 
  methodName?: String, 
  moduleName?: String, 
  moduleVersion?: String, 
  nativeMethod?: Boolean {"typeId": "boolean"}
|} {"typeAlias": "StackTraceElement",
"typeId": "java.lang.StackTraceElement"}


type java_lang_Throwable = {|
  cause?: java_lang_Throwable {"class": "java.lang.Throwable",
  "typeId": "java.lang.Throwable",
  "typeAlias": "Throwable"}, 
  localizedMessage?: String, 
  message?: String, 
  stackTrace?: Array<java_lang_StackTraceElement {"typeAlias": "StackTraceElement",
  "typeId": "java.lang.StackTraceElement"}>, 
  suppressed?: Array<java_lang_Throwable {"class": "java.lang.Throwable",
  "typeId": "java.lang.Throwable",
  "typeAlias": "Throwable"}>
|} {"class": "java.lang.Throwable",
"typeId": "java.lang.Throwable",
"typeAlias": "Throwable"}
type java_lang_StackTraceElement = {|
  classLoaderName?: String, 
  className?: String, 
  fileName?: String, 
  lineNumber?: Number, 
  methodName?: String, 
  moduleName?: String, 
  moduleVersion?: String, 
  nativeMethod?: Boolean {"typeId": "boolean"}
|} {"typeAlias": "StackTraceElement",
"typeId": "java.lang.StackTraceElement"}








type java_io_Serializable = {|  |} {"typeId": "java.io.Serializable",
"typeAlias": "Serializable"}





]]></types:shape>
    </types:type>
    <types:type name="auto_413132fc-fc5e-42d8-8f75-babfb2523476_Input-Variables-accountRequest" format="java">
      <types:shape format="raml" autogeneratedOrigin="c5a28574-ac4c-4765-8f81-41b531d3c502"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
  Climate_Action_Portal_Account_ID__c: 
    type: integer
    required: false  
  Id:
    type: string
    required: false
  Name:
    type: string
    required: true
  BillingStreet:
    type: string
    required: false
  BillingCity:
    type: string
    required: false
  BillingState:
    type: string
    required: false
  BillingPostalCode:
    type: string
    required: false
  BillingCountry:
    type: string
    required: true
  AccountSource:
    type: string
    required: true
  OwnerId:
    type: string
    required: true]]></types:shape>
    </types:type>
    <types:type name="PAYLOAD_Number" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: number]]></types:shape>
    </types:type>
    <types:type name="sfContactsResponse" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    Email:
        type: string
    Contacts:
      type: array
      items:
        properties:]]></types:shape>
    </types:type>
    <types:type name="sfContactEmailErrorResponse" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    Email:
      type: string
    Success:
      type: boolean
    Errors:
      type: array
      items:
        properties:]]></types:shape>
    </types:type>
    <types:type name="sfContactEmailResponse" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: array
items:
  properties:
    Id:
      type: string
    FirstName:
      type: string
    LastName:
      type: string
    Email:
      type: string
    AccountId:
      type: string]]></types:shape>
    </types:type>
    <types:type name="sfContactEmailRequest" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    Id: 
      type: string
    Email:
      type: string]]></types:shape>
    </types:type>
    <types:type name="sfContactEmailNotFoundErrorResponse" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    Message:
      type: string
    Email:
      type: string]]></types:shape>
    </types:type>
    <types:type name="sfContactEmailResponsePayload" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
  Contacts:
    type: array
    items:
      properties:
        Id:
          type: string
        FirstName:
          type: string
        LastName:
          type: string
        Email:
          type: string
        Domain:
          type: string
  Accounts:
    type: array
    items:
      properties:
        Id:
          type: string
        Name:
          type: string
        Email:
          type: string
        Domain:
          type: string]]></types:shape>
    </types:type>
    <types:type name="sfContactEmailDomainAccountsResponse" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: array
items:
  properties:
    Id:
      type: string
    Name:
      type: string
    Email:
      type: string  ]]></types:shape>
    </types:type>
    <types:type name="OutboundErrorResponse" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    statusCode:
      type: integer
    reasonPhrase:
      type: string
    response:
      type: array
      items:
        properties:
          message:
            type: string
          details:
            type: array
            items:
              properties:]]></types:shape>
    </types:type>
    <types:type name="sfContactDomainAccountsErrorResponse" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    Domain:
      type: string
    Success:
      type: boolean
    Errors:
      type: array
      items:
        properties:]]></types:shape>
    </types:type>
    <types:type name="erpContactEmailDomainAccountsResponse" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
  Contacts:
    type: array
    items:
      properties:
        Id:
          type: string
        FirstName:
          type: string
        LastName:
          type: string
        Email:
          type: string
        Domain:
          type: string
  Accounts:
    type: array
    items:
      properties:
        Id:
          type: string
        Name:
          type: string
        Email:
          type: string
        Domain:
          type: string]]></types:shape>
    </types:type>
    <types:type name="auto_fa60d3ea-aca7-4e87-b573-8a73351ca251_Input-Payload" format="java">
      <types:shape format="weave" example="weave/autogenerated/fa60d3ea-aca7-4e87-b573-8a73351ca251/Input-Payload.wev" autogeneratedOrigin="9bb91ed1-cf03-4cb5-9917-5a57a52843cf"><![CDATA[%dw 2.0

type auto_fa60d3ea_aca7_4e87_b573_8a73351ca251_Input_Payload = {|  |}

]]></types:shape>
    </types:type>
    <types:type name="auto_fa60d3ea-aca7-4e87-b573-8a73351ca251_Input-Attributes" format="java">
      <types:shape format="weave" example="weave/autogenerated/fa60d3ea-aca7-4e87-b573-8a73351ca251/Input-Attributes.wev" autogeneratedOrigin="9bb91ed1-cf03-4cb5-9917-5a57a52843cf"><![CDATA[%dw 2.0

type auto_fa60d3ea_aca7_4e87_b573_8a73351ca251_Input_Attributes = {|
  clientCertificate*?: {|
    publicKey?: {|  |}, 
    "type"?: String, 
    encoded?: Binary
  |}, 
  headers*: {|  |}, 
  listenerPath*: String, 
  localAddress*: String, 
  method*: String, 
  queryParams*: {|
    folderPath: String, 
    fileName: String, 
    entity: String
  |}, 
  queryString*: String, 
  relativePath*: String, 
  remoteAddress*: String, 
  requestPath*: String, 
  requestUri*: String, 
  scheme*: String, 
  uriParams*: {|  |}, 
  version*: String
|}

]]></types:shape>
    </types:type>
    <types:type name="auto_fa60d3ea-aca7-4e87-b573-8a73351ca251_Input-Variables-leadRequest" format="java">
      <types:shape format="raml" autogeneratedOrigin="9bb91ed1-cf03-4cb5-9917-5a57a52843cf"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
  Id:
    type: string
    required: false
  IsConverted:
    type: boolean
    required: true
  Climate_Action_Portal_Contact_ID__c:
    type: integer
    required: false
  Climate_Action_Portal_Account_ID__c:
    type: integer
    required: false
  FirstName: 
    type: string
    required: true
  LastName:
    type: string
    required: true
  Email: 
    type: string
    required: true
  Phone:
    type: string
    required: false
  Company:
    type: string
    required: true
  Street:
    type: string
    required: true
  City:
    type: string
    required: true
  State:
    type: string
    required: true
  PostalCode:
    type: string
    required: true
  Country:
    type: string
    required: true
  RecordTypeId: 
    type: string
    required: true
  Status: 
    type: string
    required: true
  LeadSource: 
    type: string
    required: true
  OwnerId: 
    type: string
    required: true
  Account_Name_If_Already_Exists__c: 
    type: string
    required: false]]></types:shape>
    </types:type>
    <types:type name="auto_fa60d3ea-aca7-4e87-b573-8a73351ca251_Input-Variables-httpStatus" format="java">
      <types:shape format="raml" autogeneratedOrigin="9bb91ed1-cf03-4cb5-9917-5a57a52843cf"><![CDATA[#%RAML 1.0 DataType
type: string]]></types:shape>
    </types:type>
    <types:type name="auto_fa60d3ea-aca7-4e87-b573-8a73351ca251_Input-Variables-entityInfo" format="java">
      <types:shape format="raml" autogeneratedOrigin="9bb91ed1-cf03-4cb5-9917-5a57a52843cf"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    Message:
      type: string
    Entity:
      type: object
      properties:
        Object:: 
          type: string
        Id: 
          type: string
        Name: 
          type: string
        LeadSource:
          type: string
        IsCreated:
          type: boolean
          default: false]]></types:shape>
    </types:type>
    <types:type name="auto_fa60d3ea-aca7-4e87-b573-8a73351ca251_Input-Variables-contactRequest" format="java">
      <types:shape format="raml" autogeneratedOrigin="9bb91ed1-cf03-4cb5-9917-5a57a52843cf"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
  Climate_Action_Portal_Contact_ID__c: 
    type: integer
    required: false
    default: 0  
  Id:
    type: string
    required: false
  FirstName:
    type: string
    required: true
  LastName:
    type: string
    required: true
  Email:
    type: string
    required: true
  Phone:
    type: string
    required: false
  Title:
    type: string
    required: false
  OwnerId:
    type: string
    required: true]]></types:shape>
    </types:type>
    <types:type name="auto_fa60d3ea-aca7-4e87-b573-8a73351ca251_Input-Variables-accountRequest" format="java">
      <types:shape format="raml" autogeneratedOrigin="9bb91ed1-cf03-4cb5-9917-5a57a52843cf"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
  Climate_Action_Portal_Account_ID__c: 
    type: integer
    required: false  
  Id:
    type: string
    required: false
  Name:
    type: string
    required: true
  BillingStreet:
    type: string
    required: false
  BillingCity:
    type: string
    required: false
  BillingState:
    type: string
    required: false
  BillingPostalCode:
    type: string
    required: false
  BillingCountry:
    type: string
    required: true
  AccountSource:
    type: string
    required: true
  OwnerId:
    type: string
    required: true]]></types:shape>
    </types:type>
    <types:type name="auto_fa60d3ea-aca7-4e87-b573-8a73351ca251_Output-Payload" format="java">
      <types:shape format="weave" example="weave/autogenerated/fa60d3ea-aca7-4e87-b573-8a73351ca251/Output-Payload.wev" autogeneratedOrigin="9bb91ed1-cf03-4cb5-9917-5a57a52843cf"><![CDATA[%dw 2.0

type auto_fa60d3ea_aca7_4e87_b573_8a73351ca251_Output_Payload = Any

]]></types:shape>
    </types:type>
    <types:type name="auto_fa60d3ea-aca7-4e87-b573-8a73351ca251_Output-Attributes" format="java">
      <types:shape format="weave" example="weave/autogenerated/fa60d3ea-aca7-4e87-b573-8a73351ca251/Output-Attributes.wev" autogeneratedOrigin="9bb91ed1-cf03-4cb5-9917-5a57a52843cf"><![CDATA[%dw 2.0

type auto_fa60d3ea_aca7_4e87_b573_8a73351ca251_Output_Attributes = {|
  clientCertificate?: {|
    publicKey?: {|  |}, 
    "type"?: String, 
    encoded?: Binary
  |}, 
  headers: {|  |}, 
  listenerPath: String, 
  method: String, 
  queryParams: {|
    folderPath: String, 
    fileName: String, 
    entity: String
  |}, 
  queryString: String, 
  relativePath: String, 
  remoteAddress: String, 
  requestPath: String, 
  requestUri: String, 
  scheme: String, 
  uriParams: {|  |}, 
  version: String, 
  localAddress: String
|}

]]></types:shape>
    </types:type>
    <types:type name="auto_fa60d3ea-aca7-4e87-b573-8a73351ca251_Output-Variables-outboundHeaders" format="java">
      <types:shape format="weave" example="weave/autogenerated/fa60d3ea-aca7-4e87-b573-8a73351ca251/Output-Variables-outboundHeaders.wev" autogeneratedOrigin="9bb91ed1-cf03-4cb5-9917-5a57a52843cf"><![CDATA[%dw 2.0

type auto_fa60d3ea_aca7_4e87_b573_8a73351ca251_Output_Variables_outboundHeaders = {|  |}

]]></types:shape>
    </types:type>
    <types:type name="auto_fa60d3ea-aca7-4e87-b573-8a73351ca251_Output-Variables-leadRequest" format="java">
      <types:shape format="raml" autogeneratedOrigin="9bb91ed1-cf03-4cb5-9917-5a57a52843cf"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
  Id:
    type: string
    required: false
  IsConverted:
    type: boolean
    required: true
  Climate_Action_Portal_Contact_ID__c:
    type: integer
    required: false
  Climate_Action_Portal_Account_ID__c:
    type: integer
    required: false
  FirstName: 
    type: string
    required: true
  LastName:
    type: string
    required: true
  Email: 
    type: string
    required: true
  Phone:
    type: string
    required: false
  Company:
    type: string
    required: true
  Street:
    type: string
    required: true
  City:
    type: string
    required: true
  State:
    type: string
    required: true
  PostalCode:
    type: string
    required: true
  Country:
    type: string
    required: true
  RecordTypeId: 
    type: string
    required: true
  Status: 
    type: string
    required: true
  LeadSource: 
    type: string
    required: true
  OwnerId: 
    type: string
    required: true
  Account_Name_If_Already_Exists__c: 
    type: string
    required: false]]></types:shape>
    </types:type>
    <types:type name="auto_fa60d3ea-aca7-4e87-b573-8a73351ca251_Output-Variables-salesforceError" format="java">
      <types:shape format="weave" example="weave/autogenerated/fa60d3ea-aca7-4e87-b573-8a73351ca251/Output-Variables-salesforceError.wev" autogeneratedOrigin="9bb91ed1-cf03-4cb5-9917-5a57a52843cf"><![CDATA[%dw 2.0

type auto_fa60d3ea_aca7_4e87_b573_8a73351ca251_Output_Variables_salesforceError = Null

]]></types:shape>
    </types:type>
    <types:type name="auto_fa60d3ea-aca7-4e87-b573-8a73351ca251_Output-Variables-sfContentVersionResponse" format="java">
      <types:shape format="weave" example="weave/autogenerated/fa60d3ea-aca7-4e87-b573-8a73351ca251/Output-Variables-sfContentVersionResponse.wev" autogeneratedOrigin="9bb91ed1-cf03-4cb5-9917-5a57a52843cf"><![CDATA[%dw 2.0

type auto_fa60d3ea_aca7_4e87_b573_8a73351ca251_Output_Variables_sfContentVersionResponse = Array<org_mule_runtime_api_message_Message {"typeId": "org.mule.runtime.api.message.Message"}>
type org_mule_runtime_api_message_Message = {|
  payload: ContentVersion {"typeId": "ContentVersion",
  "label": "Content Version"}, 
  attributes: Null
|} {"typeId": "org.mule.runtime.api.message.Message"}
type ContentVersion = {|
  ContentDocumentId?: String, 
  Id?: String {"typeId": "Id"}
|} {"typeId": "ContentVersion",
"label": "Content Version"}





]]></types:shape>
    </types:type>
    <types:type name="auto_fa60d3ea-aca7-4e87-b573-8a73351ca251_Output-Variables-httpStatus" format="java">
      <types:shape format="raml" autogeneratedOrigin="9bb91ed1-cf03-4cb5-9917-5a57a52843cf"><![CDATA[#%RAML 1.0 DataType
type: string]]></types:shape>
    </types:type>
    <types:type name="auto_fa60d3ea-aca7-4e87-b573-8a73351ca251_Output-Variables-entityInfo" format="java">
      <types:shape format="raml" autogeneratedOrigin="9bb91ed1-cf03-4cb5-9917-5a57a52843cf"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    Message:
      type: string
    Entity:
      type: object
      properties:
        Object:: 
          type: string
        Id: 
          type: string
        Name: 
          type: string
        LeadSource:
          type: string
        IsCreated:
          type: boolean
          default: false]]></types:shape>
    </types:type>
    <types:type name="auto_fa60d3ea-aca7-4e87-b573-8a73351ca251_Output-Variables-contactRequest" format="java">
      <types:shape format="raml" autogeneratedOrigin="9bb91ed1-cf03-4cb5-9917-5a57a52843cf"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
  Climate_Action_Portal_Contact_ID__c: 
    type: integer
    required: false
    default: 0  
  Id:
    type: string
    required: false
  FirstName:
    type: string
    required: true
  LastName:
    type: string
    required: true
  Email:
    type: string
    required: true
  Phone:
    type: string
    required: false
  Title:
    type: string
    required: false
  OwnerId:
    type: string
    required: true]]></types:shape>
    </types:type>
    <types:type name="auto_fa60d3ea-aca7-4e87-b573-8a73351ca251_Output-Variables-accountRequest" format="java">
      <types:shape format="raml" autogeneratedOrigin="9bb91ed1-cf03-4cb5-9917-5a57a52843cf"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
  Climate_Action_Portal_Account_ID__c: 
    type: integer
    required: false  
  Id:
    type: string
    required: false
  Name:
    type: string
    required: true
  BillingStreet:
    type: string
    required: false
  BillingCity:
    type: string
    required: false
  BillingState:
    type: string
    required: false
  BillingPostalCode:
    type: string
    required: false
  BillingCountry:
    type: string
    required: true
  AccountSource:
    type: string
    required: true
  OwnerId:
    type: string
    required: true]]></types:shape>
    </types:type>
    <types:type name="auto_fa60d3ea-aca7-4e87-b573-8a73351ca251_Output-Variables-sfContentDocumentLinkResponse" format="java">
      <types:shape format="weave" example="weave/autogenerated/fa60d3ea-aca7-4e87-b573-8a73351ca251/Output-Variables-sfContentDocumentLinkResponse.wev" autogeneratedOrigin="9bb91ed1-cf03-4cb5-9917-5a57a52843cf"><![CDATA[%dw 2.0

type auto_fa60d3ea_aca7_4e87_b573_8a73351ca251_Output_Variables_sfContentDocumentLinkResponse = {|
  id?: java_io_Serializable {"typeId": "java.io.Serializable",
  "typeAlias": "Serializable"}, 
  items?: Array<org_mule_runtime_api_bulk_BulkItem {"typeAlias": "BulkItem",
  "typeId": "org.mule.runtime.api.bulk.BulkItem"}>, 
  successful?: Boolean {"typeId": "boolean"}
|} {"typeAlias": "BulkOperationResult",
"typeId": "org.mule.runtime.api.bulk.BulkOperationResult"}
type org_mule_runtime_api_bulk_BulkItem = {|
  exception?: java_lang_Exception {"typeId": "java.lang.Exception",
  "typeAlias": "Exception",
  "class": "java.lang.Exception"}, 
  id?: java_io_Serializable_38 {"typeId": "java.io.Serializable",
  "typeAlias": "Serializable"}, 
  message?: String, 
  payload?: org_mule_extension_salesforce_api_core_Result {"typeAlias": "Result",
  "class": "org.mule.extension.salesforce.api.core.Result",
  "typeId": "org.mule.extension.salesforce.api.core.Result"}, 
  statusCode?: String, 
  successful?: Boolean {"typeId": "boolean"}
|} {"typeAlias": "BulkItem",
"typeId": "org.mule.runtime.api.bulk.BulkItem"}
type org_mule_extension_salesforce_api_core_Result = {|
  errors?: Array<org_mule_extension_salesforce_api_core_Error {"class": "org.mule.extension.salesforce.api.core.Error",
  "typeAlias": "Error",
  "typeId": "org.mule.extension.salesforce.api.core.Error"}>, 
  id?: String, 
  success?: Boolean {"typeId": "boolean"}
|} {"typeAlias": "Result",
"class": "org.mule.extension.salesforce.api.core.Result",
"typeId": "org.mule.extension.salesforce.api.core.Result"}
type org_mule_extension_salesforce_api_core_Error = {|
  duplicateResult?: org_mule_extension_salesforce_api_core_DuplicateResult {"typeAlias": "DuplicateResult",
  "class": "org.mule.extension.salesforce.api.core.DuplicateResult",
  "typeId": "org.mule.extension.salesforce.api.core.DuplicateResult"}, 
  fields?: Array<String>, 
  message?: String, 
  statusCode?: String
|} {"class": "org.mule.extension.salesforce.api.core.Error",
"typeAlias": "Error",
"typeId": "org.mule.extension.salesforce.api.core.Error"}
type org_mule_extension_salesforce_api_core_DuplicateResult = {|
  allowSave?: Boolean {"typeId": "boolean"}, 
  duplicateRule?: String, 
  duplicateRuleEntityType?: String, 
  errorMessage?: String, 
  matchResults?: Array<org_mule_extension_salesforce_api_core_MatchResult {"typeId": "org.mule.extension.salesforce.api.core.MatchResult",
  "typeAlias": "MatchResult",
  "class": "org.mule.extension.salesforce.api.core.MatchResult"}>
|} {"typeAlias": "DuplicateResult",
"class": "org.mule.extension.salesforce.api.core.DuplicateResult",
"typeId": "org.mule.extension.salesforce.api.core.DuplicateResult"}
type org_mule_extension_salesforce_api_core_MatchResult = {|
  entityType?: String, 
  errors?: Array<org_mule_extension_salesforce_api_core_SimpleError {"class": "org.mule.extension.salesforce.api.core.SimpleError",
  "typeAlias": "SimpleError",
  "typeId": "org.mule.extension.salesforce.api.core.SimpleError"}>, 
  matchEngine?: String, 
  matchRecords?: Array<org_mule_extension_salesforce_api_utility_MatchRecord {"typeAlias": "MatchRecord",
  "class": "org.mule.extension.salesforce.api.utility.MatchRecord",
  "typeId": "org.mule.extension.salesforce.api.utility.MatchRecord"}>, 
  rule?: String, 
  size?: Number, 
  success?: Boolean {"typeId": "boolean"}
|} {"typeId": "org.mule.extension.salesforce.api.core.MatchResult",
"typeAlias": "MatchResult",
"class": "org.mule.extension.salesforce.api.core.MatchResult"}
type org_mule_extension_salesforce_api_core_SimpleError = {|
  fields?: Array<String>, 
  message?: String, 
  statusCode?: String
|} {"class": "org.mule.extension.salesforce.api.core.SimpleError",
"typeAlias": "SimpleError",
"typeId": "org.mule.extension.salesforce.api.core.SimpleError"}


type org_mule_extension_salesforce_api_utility_MatchRecord = {|
  additionalInformation?: Array<org_mule_extension_salesforce_api_utility_AdditionalInformationMap {"typeAlias": "AdditionalInformationMap",
  "typeId": "org.mule.extension.salesforce.api.utility.AdditionalInformationMap",
  "class": "org.mule.extension.salesforce.api.utility.AdditionalInformationMap"}>, 
  fieldDiffs?: Array<org_mule_extension_salesforce_api_utility_FieldDiff {"class": "org.mule.extension.salesforce.api.utility.FieldDiff",
  "typeAlias": "FieldDiff",
  "typeId": "org.mule.extension.salesforce.api.utility.FieldDiff"}>, 
  matchConfidence?: Number, 
  record?: { _?: java_lang_Object {"class": "java.lang.Object",
    "typeAlias": "Object",
    "typeId": "java.lang.Object"} }
|} {"typeAlias": "MatchRecord",
"class": "org.mule.extension.salesforce.api.utility.MatchRecord",
"typeId": "org.mule.extension.salesforce.api.utility.MatchRecord"}
type org_mule_extension_salesforce_api_utility_FieldDiff = {|
  difference?: ("DIFFERENT" | "NULL" | "SAME" | "SIMILAR") {"typeId": "org.mule.extension.salesforce.api.utility.DifferenceType"}, 
  name?: String
|} {"class": "org.mule.extension.salesforce.api.utility.FieldDiff",
"typeAlias": "FieldDiff",
"typeId": "org.mule.extension.salesforce.api.utility.FieldDiff"}


type org_mule_extension_salesforce_api_utility_AdditionalInformationMap = {|
  name?: String, 
  value?: String
|} {"typeAlias": "AdditionalInformationMap",
"typeId": "org.mule.extension.salesforce.api.utility.AdditionalInformationMap",
"class": "org.mule.extension.salesforce.api.utility.AdditionalInformationMap"}


type java_lang_Object = {|  |} {"class": "java.lang.Object",
"typeAlias": "Object",
"typeId": "java.lang.Object"}












type java_io_Serializable_38 = {|  |} {"typeId": "java.io.Serializable",
"typeAlias": "Serializable"}


type java_lang_Exception = {|
  cause?: java_lang_Throwable {"class": "java.lang.Throwable",
  "typeId": "java.lang.Throwable",
  "typeAlias": "Throwable"}, 
  localizedMessage?: String, 
  message?: String, 
  stackTrace?: Array<java_lang_StackTraceElement_15 {"typeAlias": "StackTraceElement",
  "typeId": "java.lang.StackTraceElement"}>, 
  suppressed?: Array<java_lang_Throwable_25 {"class": "java.lang.Throwable",
  "typeId": "java.lang.Throwable",
  "typeAlias": "Throwable"}>
|} {"typeId": "java.lang.Exception",
"typeAlias": "Exception",
"class": "java.lang.Exception"}
type java_lang_Throwable_25 = {|
  cause?: java_lang_Throwable_25 {"class": "java.lang.Throwable",
  "typeId": "java.lang.Throwable",
  "typeAlias": "Throwable"}, 
  localizedMessage?: String, 
  message?: String, 
  stackTrace?: Array<java_lang_StackTraceElement_29 {"typeAlias": "StackTraceElement",
  "typeId": "java.lang.StackTraceElement"}>, 
  suppressed?: Array<java_lang_Throwable_25 {"class": "java.lang.Throwable",
  "typeId": "java.lang.Throwable",
  "typeAlias": "Throwable"}>
|} {"class": "java.lang.Throwable",
"typeId": "java.lang.Throwable",
"typeAlias": "Throwable"}
type java_lang_StackTraceElement_29 = {|
  classLoaderName?: String, 
  className?: String, 
  fileName?: String, 
  lineNumber?: Number, 
  methodName?: String, 
  moduleName?: String, 
  moduleVersion?: String, 
  nativeMethod?: Boolean {"typeId": "boolean"}
|} {"typeAlias": "StackTraceElement",
"typeId": "java.lang.StackTraceElement"}




type java_lang_StackTraceElement_15 = {|
  classLoaderName?: String, 
  className?: String, 
  fileName?: String, 
  lineNumber?: Number, 
  methodName?: String, 
  moduleName?: String, 
  moduleVersion?: String, 
  nativeMethod?: Boolean {"typeId": "boolean"}
|} {"typeAlias": "StackTraceElement",
"typeId": "java.lang.StackTraceElement"}


type java_lang_Throwable = {|
  cause?: java_lang_Throwable {"class": "java.lang.Throwable",
  "typeId": "java.lang.Throwable",
  "typeAlias": "Throwable"}, 
  localizedMessage?: String, 
  message?: String, 
  stackTrace?: Array<java_lang_StackTraceElement {"typeAlias": "StackTraceElement",
  "typeId": "java.lang.StackTraceElement"}>, 
  suppressed?: Array<java_lang_Throwable {"class": "java.lang.Throwable",
  "typeId": "java.lang.Throwable",
  "typeAlias": "Throwable"}>
|} {"class": "java.lang.Throwable",
"typeId": "java.lang.Throwable",
"typeAlias": "Throwable"}
type java_lang_StackTraceElement = {|
  classLoaderName?: String, 
  className?: String, 
  fileName?: String, 
  lineNumber?: Number, 
  methodName?: String, 
  moduleName?: String, 
  moduleVersion?: String, 
  nativeMethod?: Boolean {"typeId": "boolean"}
|} {"typeAlias": "StackTraceElement",
"typeId": "java.lang.StackTraceElement"}








type java_io_Serializable = {|  |} {"typeId": "java.io.Serializable",
"typeAlias": "Serializable"}



]]></types:shape>
    </types:type>
    <types:type name="artInsertRequest" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    database:
      type: string
      required: true
    schema:
      type: string
      required: true
    table:
      type: string
      required: true
    value:
      type: array
      items:
        properties:]]></types:shape>
    </types:type>
    <types:type name="artErrorResponse" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    errors:
      type: string | object | array]]></types:shape>
    </types:type>
    <types:type name="sfContactCreateErrorResponse" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: array
items:
  properties:
    Message:
      type: string
    ErrorCode:
      type: string
    Fields:
      type: array
      required: false
      default: []
    Duplicates:
      type: array
      required: false
      default: []
    ]]></types:shape>
    </types:type>
    <types:type name="sfAuthTokenRefreshResponse" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    message:
      type: string
    success:
      type: boolean]]></types:shape>
    </types:type>
    <types:type name="sfAuthAccessTokenResponse" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    access_token:
      type: string
    signature:
      type: string
    scope:
      type: string
    instance_url:
      type: string
    id:
      type: string
    token_type:
      type: string
    issued_at:
      type: string]]></types:shape>
    </types:type>
    <types:type name="sfApiErrorResponse" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    Message:
      type: string
    Error:
      type: string]]></types:shape>
    </types:type>
    <types:type name="sfAuthRequestBody" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    grant_type:
      type: string
    client_id:
      type: string
    client_secret:
      type: string
    refresh_token:
      type: string]]></types:shape>
    </types:type>
    <types:type name="sfLeadConvertedResponse" format="java">
      <types:shape format="raml"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    leadId:
      type: string
    contactId:
      type: string
    accountId:
      type: string
    opportunityId:
      type: string]]></types:shape>
    </types:type>
    <types:type name="auto_582641ce-9459-468a-8d02-ca2b724215ea_Output-Variables-objectStore" format="java">
      <types:shape format="raml" autogeneratedOrigin="a28ab5b8-2709-4522-a499-0cced7608e60"><![CDATA[#%RAML 1.0 DataType
type: string]]></types:shape>
    </types:type>
    <types:type name="auto_********-fd7e-43fd-b257-34a308d21c50_Output-Payload" format="java">
      <types:shape format="raml" autogeneratedOrigin="8c452873-33cd-4a66-93ba-51662f9c056d"><![CDATA[#%RAML 1.0 DataType
type: object
properties:
    message:
      type: string
    success:
      type: boolean]]></types:shape>
    </types:type>
    <types:type name="auto_********-fd7e-43fd-b257-34a308d21c50_Output-Attributes" format="java">
      <types:shape format="weave" example="weave/autogenerated/********-fd7e-43fd-b257-34a308d21c50/Output-Attributes.wev" autogeneratedOrigin="8c452873-33cd-4a66-93ba-51662f9c056d"><![CDATA[%dw 2.0

type auto_********_fd7e_43fd_b257_34a308d21c50_Output_Attributes = Null

]]></types:shape>
    </types:type>
  </types:catalog>
  <types:enrichment select="#cf30891d-548c-4c13-a19b-c3813b948795">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="stringPayload"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="kvpExample"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#5f34d1e7-a0ea-44c3-bba6-d2f50aba89cc">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="kvpExample"/>
        </types:message>
      </types:input-event>
      <types:output-event>
        <types:message>
          <types:payload type="kvpExample"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#1e7e82ae-e61d-49ba-b739-5cb71fe36c5f">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="kvpExample"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#4dbfd318-040b-442c-a7ea-9644eae7768c">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="kvpExample"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#0e93d0fd-bf03-46a4-ab65-6f03665a1edc">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="xeroAuthHeaderExample"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="xeroAuthHeaderExample"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#6ca414bd-5853-4c90-aeef-8fcc4945bed0">
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="kvpExample"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#46011b07-c3be-4599-8ec9-b1661673a045">
    <types:processor-declaration>
      <types:input-event>
        <types:variables>
          <types:variable name="Config" type="storeXeroAuthPostExample"/>
        </types:variables>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#13185eef-36d5-437d-b662-dc786e14fdd7">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="xeroInvoicesPayloadExample"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#77fd2ee6-4a29-47f4-95c8-c47a2706f2db">
    <types:processor-declaration>
      <types:input-event>
        <types:variables>
          <types:variable name="invoices_attachments" type="xeroInvoicesAttachmentsExample"/>
          <types:variable name="attachments" type="AttachmentsPayloadResponseExample"/>
          <types:variable name="invoices" type="xeroInvoicesPayloadExample"/>
          <types:variable name="confirm_invoices" type="xeroConfirmsInvoicesExample"/>
        </types:variables>
      </types:input-event>
    </types:processor-declaration>
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="xeroConfirmInvoicesExample"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#a42a73be-d606-4416-ae29-13af343a2427">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="xeroOnlineInvoicesPayloadExample"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="xeroInvoiceUrlsExample"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#53698c77-a354-488b-825b-4730f847c679">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="xeroInvoicesDocumentsRequestExample"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="xeroInvoicesDocumentsRequestExample"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#11517f4d-3911-4c44-a0c8-454b43f15b9c">
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="stringPayload"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#2f6b7000-c8d4-4cf8-9729-0d4a26cad8a2">
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="xeroInvoiceUrlsExample"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#ccd9aec3-c19e-4485-82c0-5b1162c03cd1">
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="xeroInvoiceUrlsExample"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#cb95e42e-0706-4735-87e9-669731329695">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="xeroConfirmsInvoicesExample"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="xeroConfirmsInvoicesExample"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#40fb7971-d83a-4cb5-8549-cfa2ec97c90a">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="confirmListRequestExample"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="confirmListRequestExample"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#3f52fed3-16e7-4e9d-a73f-7c30b5ceab39">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="xeroInvoiceUrlsExample"/>
        </types:message>
      </types:input-event>
      <types:output-event>
        <types:message>
          <types:payload type="xeroInvoicesDocumentsInfoPayloadExample"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#b00bcd75-c540-49d1-80c1-3e4b15c7f2be">
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="xeroInvoiceUrlsExample"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#3c42440f-d226-4d01-9f48-bcbb00bdc670">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="storeXeroAuthResponseExample"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#8fdda4bf-04eb-410b-84c4-c8c04d53c23e">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="storeXeroAuthResponseExample"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#e511c59b-03f5-40bb-9584-4e0ec7adb7dc">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="storeXeroAuthResponseExample"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#7b0685b8-71fc-4614-9451-d38fa2b51bf0">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="xeroOnlineInvoicesPayloadExample"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="xeroInvoiceUrlsExample"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#2f1fe99c-6759-4fdd-93f8-82a35906b7da">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="storeXeroAuthResponseExample"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#fc42735f-5b0d-4f05-9924-17019bce6691">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="storeXeroAuthResponseExample"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#15382f7e-b612-4014-a355-15d516d7f453">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="xeroAuthTokensExample"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#3ef8c538-1387-48cc-9d16-acd29d2bcbd8">
    <types:processor-declaration>
      <types:output-event>
        <types:variables>
          <types:variable name="xero_auth" type="storeXeroAuthResponseExample"/>
        </types:variables>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#e5034cd2-f89d-4641-bc54-6b49b497c0c1">
    <types:processor-declaration>
      <types:output-event>
        <types:variables>
          <types:variable name="xero_auth" type="storeXeroAuthResponseExample"/>
        </types:variables>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#140ebbe5-250e-4784-825b-88d5c019933c">
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="xeroInvoiceUrlsExample"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#b1e73fb7-04f4-44b3-aaa8-e00a92b45cb3">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="xeroInvoicesDocumentsInfoPayloadExample"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="xeroInvoicesDocumentsInfoPayloadExample"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#697dec8e-9397-4cca-b3d2-66d263f032a2">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="xeroInvoicesPayloadExample"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="xeroInvoicesAttachmentsExample"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#2cb8c40c-a53a-48b9-8431-c8359550bac1">
    <types:processor-declaration>
      <types:input-event>
        <types:variables>
          <types:variable name="current" type="xeroInvoicesAttachmentsExample"/>
        </types:variables>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#5695da64-e4b6-407e-9e2a-10db88f1a74f">
    <types:processor-declaration>
      <types:input-event>
        <types:variables>
          <types:variable name="attachments_response" type="xeroInvoicesAttachmentsExample"/>
        </types:variables>
      </types:input-event>
    </types:processor-declaration>
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="AttachmentsPayloadResponseExample"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#a4d284fa-0755-448d-a9d1-f76b554d5e0c">
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="xeroConfirmsInvoicesExample"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#8a38be30-9698-498d-8b7b-a415da0f0c42">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="confirmListRequestExample"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#6a7e254f-a774-48f5-a5cf-f18fd14a2ec9">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="salesforceOpportunitiesClosedWon[]"/>
        </types:message>
        <types:variables>
          <types:variable name="oppportunities" type="salesforceOpportunitiesClosedWon[]"/>
        </types:variables>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#efe3fc57-aa2d-422a-85d7-2c1703dcab17">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforceOpportunitiesClosedWon[]"/>
        </types:message>
        <types:variables>
          <types:variable name="opportunities" type="salesforceOpportunitiesClosedWon[]"/>
        </types:variables>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#15ecefc8-a80d-4004-bda7-e336418f9e92">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="stringPayload"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#277388f6-9df6-46a9-babc-0745da6d0eea">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="ArtemisDocumentExample[]"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#9ecf50ad-8bce-48b9-bd0d-3afe9e565714">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="supplierReachLeadCreateResponse"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#d29af025-609f-443e-9021-01932233629c">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="climatePortalSupplierReachPurchase"/>
        </types:message>
      </types:input-event>
      <types:output-event>
        <types:message>
          <types:payload type="climatePortalSupplierReachPurchase"/>
        </types:message>
        <types:variables>
          <types:variable name="purchaseProducts" type="climatePortalSupplierReachPurchaseProducts"/>
        </types:variables>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#5120d686-62b2-4ce2-9ec1-fb576236db90">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="climatePortalMarketplacePurchase"/>
        </types:message>
      </types:input-event>
      <types:output-event>
        <types:message>
          <types:payload type="salesforceMarketplaceLeadRequest"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#6faddef1-afb5-42c6-9ca5-cd75cb2c0ee8">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="artemisApiDocumentList"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="FileInfo"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#1af453c9-6714-4adf-b2ab-74a2e62f273e">
    <types:processor-declaration>
      <types:input-event>
        <types:variables>
          <types:variable name="fileInfo" type="FileInfo"/>
        </types:variables>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#56cba20e-080a-4e9b-8de6-f9495f017240">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="climatePortalSupplierReachPurchase"/>
        </types:message>
      </types:input-event>
      <types:output-event>
        <types:variables>
          <types:variable name="request" type="requestPayload"/>
        </types:variables>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#522804be-84f2-44a7-b7fd-547dc6f33391">
    <types:processor-declaration>
      <types:output-event>
        <types:variables>
          <types:variable name="request" type="requestPayload"/>
        </types:variables>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#dc9be18e-84a8-4d0a-ab1e-582340f67658">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="supplierReachPurchaseResponse"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#460c4b4d-db2a-457f-8283-2c5cfd9d54c0">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="SalesforceProduct"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#3968e3dd-03e2-43f9-b950-8fda13f6c341">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="SalesforceProduct"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#b3104324-bfc6-4a27-9ba2-ad4c92783c7d">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="salesforceOpportunitiesClosedWon"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#3418f1d3-9f1e-4a53-8723-3c14f4a9ffa2">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="SalesforceAccount"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#a90721f4-7bf2-47d4-8a9a-b0dc5ea17c4d">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="SalesforceEntityDocumentResponse"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#f95fe133-a4bb-422d-a2ac-0cafae38a95c">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="SalesforceDocumentsResponse"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#959c9517-353e-4ab7-8117-e3833fa147b6">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="SalesforceDocumentsResponse"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#27e5a2c5-2009-4c71-a09a-d64b870da389">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="SalesforceAccountOpportunityIdList"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#8905a8c5-6476-40a0-b74d-71770b7114fd">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="SalesforceEntityDocumentResponse"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#57d6be91-aa8a-4254-91f4-089234450e85">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="PAYLOAD_String"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#df2ddc12-7e68-4203-a6bf-bfe70c203a5c">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="MarketplaceLeadRequest"/>
        </types:message>
      </types:input-event>
      <types:output-event>
        <types:variables>
          <types:variable name="request" type="requestPayload"/>
          <types:variable name="leadName" type="PAYLOAD_String"/>
        </types:variables>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#06ccd13b-d17c-41d4-b992-6b6e93d91687">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="MarketplaceLeadRequest"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="MarketplaceLeadRequest"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#eedef182-c627-4293-95e9-f8d6b57f201a">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="LeadRequest"/>
        </types:message>
      </types:input-event>
      <types:output-event>
        <types:message>
          <types:payload type="sfLeadRequest"/>
        </types:message>
        <types:variables>
          <types:variable name="accountId" type="PAYLOAD_String"/>
          <types:variable name="opportunityId" type="PAYLOAD_String"/>
          <types:variable name="contactId" type="PAYLOAD_String"/>
        </types:variables>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#4efeb8dc-a919-45c3-9430-debb8a54b31b">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="LeadRequest"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#f7752f5b-92c3-4ebc-9819-4689c19d5bba">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="LeadRequest"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#35bb37d9-3690-48b0-be66-5f9d7aab616d">
    <types:processor-declaration>
      <types:output-event>
        <types:variables>
          <types:variable name="request" type="Request"/>
        </types:variables>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#f87ebb18-9ce3-41e5-bf7a-26598dfd18a2">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
      <types:output-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#6ca0d431-02ec-4e24-9048-e14969f75c57">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="EntityInfo"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#34ecf940-b72c-48c8-b5d1-44b5c19f5c0d">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="sfMarketplacePurchase"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#d489b51d-404f-46bf-a68c-1dfd9aa89785">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#2e69b88b-3832-474d-a3f6-c098a2ba4e49">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
      <types:output-event>
        <types:variables>
          <types:variable name="leadRequest" type="sfMarketplaceLeadRequest"/>
        </types:variables>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#f452ab59-4ade-435e-9b06-fb48aeaccd68">
    <types:processor-declaration>
      <types:output-event>
        <types:variables>
          <types:variable name="request" type="Request"/>
        </types:variables>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#18a9fb34-2838-430b-b29d-21b45c7a3ddd">
    <types:processor-declaration>
      <types:input-event>
        <types:variables>
          <types:variable name="request" type="Request"/>
        </types:variables>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#ccad100f-8be5-47bc-af63-1e08000afea7">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="sfContactEmailResponsePayload"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#074354ce-96a2-41cb-b450-1e36307228cb">
    <types:processor-declaration>
      <types:input-event>
        <types:variables>
          <types:variable name="request" type="Request"/>
        </types:variables>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#69df6c0b-aa9d-44c1-8355-252bc716925f">
    <types:processor-declaration>
      <types:output-event>
        <types:variables>
          <types:variable name="sfDomainAccountsResponse" type="EmailDomainAccountsResponse"/>
        </types:variables>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#1216bdf2-8bb3-4599-b3cf-dbdbe806a18c">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
      <types:output-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#d0c4cbee-15b7-4bc5-ac69-08365fb3abd9">
    <types:processor-declaration>
      <types:output-event>
        <types:variables>
          <types:variable name="leadRequest" type="salesforceMarketplaceLeadRequest"/>
        </types:variables>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#db47c05e-a696-4945-9227-3c2ea94fa229">
    <types:processor-declaration>
      <types:output-event>
        <types:variables>
          <types:variable name="opportunityLineItemsRequest" type="salesforceOpportunityLineItemsRequest"/>
          <types:variable name="contactRequest" type="salesforceMarketplaceContactRequest"/>
          <types:variable name="accountRequest" type="salesforceMarketplaceAccountRequest"/>
        </types:variables>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#35b45115-ff12-4293-bd9a-3de46bfd0c4a">
    <types:processor-declaration>
      <types:input-event>
        <types:variables>
          <types:variable name="contactRequest" type="salesforceMarketplaceContactRequest"/>
        </types:variables>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#1a3563db-34f3-4bac-ae3d-e1d780ef146a">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#75d1ab6b-cf83-49df-b918-b1c8db8dc523">
    <types:processor-declaration>
      <types:input-event>
        <types:variables>
          <types:variable name="accountRequest" type="salesforceMarketplaceAccountRequest"/>
        </types:variables>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#********-ffbf-4b82-9d4f-c5fc481f08e0">
    <types:processor-declaration>
      <types:input-event>
        <types:variables>
          <types:variable name="accountRequest" type="salesforceMarketplaceAccountRequest"/>
        </types:variables>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#4f5f5298-daae-43b9-b820-b292fe604df5">
    <types:processor-declaration>
      <types:output-event>
        <types:variables>
          <types:variable name="entityInfo" type="EntityInfo"/>
        </types:variables>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#7c245336-7bd5-415a-a64a-5ec5e75528c1">
    <types:processor-declaration>
      <types:output-event>
        <types:variables>
          <types:variable name="leadRequest" type="salesforceSupplierREachLeadRequest"/>
          <types:variable name="filesRequest" type="salesforceSupplierREachFileRequest"/>
        </types:variables>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#789f5d18-b46e-4656-b777-67cfdbc90358">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
      <types:output-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#67cb9074-4a5c-48de-8504-2cc2318bc527">
    <types:processor-declaration>
      <types:output-event>
        <types:variables>
          <types:variable name="filesRequest" type="salesforceSupplierREachFileRequest"/>
          <types:variable name="entityInfo" type="EntityInfo"/>
          <types:variable name="contactRequest" type="salesforceSupplierREachContactRequest"/>
          <types:variable name="accountRequest" type="salesforceSupplierREachAccountRequest"/>
        </types:variables>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#c732b3cd-3608-4a9a-819f-e126165c9c66">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
      <types:output-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#523d1081-5ee5-4957-8a1a-d0f4b1788320">
    <types:processor-declaration>
      <types:output-event>
        <types:variables>
          <types:variable name="entityInfo" type="EntityInfo"/>
        </types:variables>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#3a5794db-33e8-4e6b-a79a-10b5a1dd91dd">
    <types:processor-declaration>
      <types:output-event>
        <types:variables>
          <types:variable name="sfLeadChanges" type="sfLeadChangesResponse"/>
        </types:variables>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#12f08805-8a10-4bde-8a22-86adb32233a2">
    <types:processor-declaration>
      <types:output-event>
        <types:variables>
          <types:variable name="leadRequest" type="salesforceMarketplaceLeadRequest"/>
          <types:variable name="opportunityLineItemsRequest" type="salesforceOpportunityLineItemsRequest"/>
          <types:variable name="opportunityRequest" type="salesforceOpportunityRequest"/>
          <types:variable name="contactRequest" type="salesforceMarketplaceContactRequest"/>
          <types:variable name="accountRequest" type="salesforceMarketplaceAccountRequest"/>
        </types:variables>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#415fc26c-b532-4ad8-8ec6-fb780a0d7e8f">
    <types:processor-declaration>
      <types:output-event>
        <types:variables>
          <types:variable name="leadRequest" type="salesforceSupplierREachLeadRequest"/>
        </types:variables>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#1450c922-4e21-4685-8045-521cf3cf8b2d">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforceOpportunityLineItemsRequest"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#2e102a5a-bd4a-4076-9778-74516b782f39">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#0dcb9418-74e5-4aad-a190-48b954749467">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
      <types:output-event>
        <types:variables>
          <types:variable name="leadRequest" type="salesforceMarketplaceLeadRequest"/>
          <types:variable name="entityInfo" type="EntityInfo"/>
          <types:variable name="contactRequest" type="salesforceMarketplaceContactRequest"/>
          <types:variable name="accountRequest" type="salesforceMarketplaceAccountRequest"/>
        </types:variables>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#7b89e9ed-512e-4a02-a394-aa000c5d51e0">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
      <types:output-event>
        <types:variables>
          <types:variable name="accountId" type="PAYLOAD_String"/>
          <types:variable name="opportunityId" type="PAYLOAD_String"/>
          <types:variable name="contactId" type="PAYLOAD_String"/>
        </types:variables>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#11695bc8-7b5d-41bc-bfad-aba550fc7c7c">
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="Errors"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#25d93419-e38e-4c39-b5b4-fe2f2797b36b">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#2125d59e-1e7b-4d6a-83f1-7655bfd1ec1d">
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="Errors"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#cf019fb7-d4ef-419f-a71f-ffbde9df1980">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
        <types:variables>
          <types:variable name="isCsvFileCreated" type="salesforceMarketplacePricebook"/>
        </types:variables>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#fa7dfaa3-b80d-417a-8266-28e6720ae079">
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="Errors"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#794ff486-ac75-480f-b750-de89d67c6eb8">
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="sfErrorsResponse"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#058a6350-821d-4413-873e-81945b80035a">
    <types:processor-declaration>
      <types:output-event>
        <types:variables>
          <types:variable name="osEntry" type="ObjectStoreEntry"/>
        </types:variables>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#6f33dd11-c2e2-46bf-b6da-7925230f0060">
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="Errors"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#3eed56fc-8e2e-42e8-921b-9c1eb825060f">
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="salesforceErrors"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#de7a4ccf-4841-4056-8468-58df36479a39">
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="sfErrorsResponse"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#eceea81f-4297-4080-84e0-c811f7765a05">
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="sfErrorsResponse"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#a8b8d8de-694e-460e-add1-e30c42f13024">
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="sfErrorsResponse"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#99fd26ca-b155-43ba-bd32-247ea174a389">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#3b61f79b-f2dc-412e-b1ad-ea5c01201ead">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="Paging"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#76ec1832-b7d1-42ed-991a-08035c946830">
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="AuthClient"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#6a916bc5-00fd-4383-99ec-7b08de99aefa">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="artemisAuthClient"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#467b88c3-9ada-4ff3-bdba-9f83da6e764d">
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="queryParams" type="artemisApiTableRequest"/>
        <types:parameter name="headers" type="AuthClient"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#4cb0dad6-f647-49a7-9899-dd7e12fed3a1">
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="queryParams" type="artemisApiStoredProcedureRequest"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#e3b9ffc5-4461-4e77-b329-3befc6dab01a">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="OutboundArrayResponse"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#2dcf707e-8a50-4594-ada6-f2ef9836f2d1">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="OutboundObjectResponse"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#302d0af5-6054-48c1-bf05-8c6cab1d038f">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="OutboundObjectResponse"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#e7024a18-2c20-4dd5-b0ca-485e3221e89e">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="OutboundObjectResponse"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#8309d663-996a-40e5-83bf-ab1672c9d357">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="OutboundObjectResponse"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#f796bb35-b880-438b-b9bd-2e788db48969">
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="OutboundObjectResponse"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#59063ebe-de91-4dc6-8b89-5ea43bc674ec">
    <types:processor-declaration>
      <types:input-event>
        <types:variables>
          <types:variable name="osEntry" type="ObjectStoreEntry"/>
        </types:variables>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#8f4e42af-3c1c-473c-b944-0313b6cf6670">
    <types:processor-declaration>
      <types:output-event>
        <types:variables>
          <types:variable name="osEntry" type="ObjectStoreEntry"/>
        </types:variables>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#3c1df4e4-f499-4e44-ab7d-7a81b3038df2">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#78d1034e-e88f-4a39-80fe-6a8c378ff897">
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="sfErrorsResponse"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#f24d9d70-4a7e-4b96-9f6c-91185e5e0c1f">
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="sfErrorsResponse"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#afa6adf2-2967-4bad-98bd-8b17ee0f267e">
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="sfErrorsOpportunityUpsert"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#f793daeb-9b81-439e-9392-2fbb3999b8a8">
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="sfErrorsLeadUpsert"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#5b8031ab-0c3f-4916-8c00-e24adb4f172e">
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="parameters" type="salesorceOpportunityRequest"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#2bfb7d8d-779a-4d63-8679-f63a231b067a">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="sfAccountConfirmResponse"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#c4e6acfe-f28c-481d-a0f9-5e9a1eae568f">
    <types:processor-declaration>
      <types:output-event>
        <types:variables>
          <types:variable name="sfOpportunityQuery" type="PAYLOAD_String"/>
        </types:variables>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#f7a8948f-02f1-498e-94d3-f9dcc0918b28">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="sfOpportunityResponsePayload"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#46ce7f4b-1e31-4ed5-84a5-0adda1eefd6f">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="CountryRegionResponse"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#671cc994-919c-4b73-8bd1-8ea82562cbd6">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
      <types:output-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#32c21a26-0d1e-4c8c-9d1c-56a2d39e456f">
    <types:processor-declaration>
      <types:output-event>
        <types:variables>
          <types:variable name="entityInfo" type="EntityInfo"/>
        </types:variables>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#836b776a-fa1c-404c-8e39-1a1ea1a9a41a">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="PAYLOAD_Object"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#b377bd6f-ba86-4401-84af-a5f6da156fa6">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="auto_b377bd6f-ba86-4401-84af-a5f6da156fa6_Input-Payload"/>
          <types:attributes type="auto_b377bd6f-ba86-4401-84af-a5f6da156fa6_Input-Attributes"/>
        </types:message>
        <types:variables>
          <types:variable name="isLeadConverted" type="auto_b377bd6f-ba86-4401-84af-a5f6da156fa6_Input-Variables-isLeadConverted"/>
          <types:variable name="outboundHeaders" type="auto_b377bd6f-ba86-4401-84af-a5f6da156fa6_Input-Variables-outboundHeaders"/>
          <types:variable name="opportunityId" type="auto_b377bd6f-ba86-4401-84af-a5f6da156fa6_Input-Variables-opportunityId"/>
          <types:variable name="isCsvFileCreated" type="auto_b377bd6f-ba86-4401-84af-a5f6da156fa6_Input-Variables-isCsvFileCreated"/>
          <types:variable name="httpStatus" type="auto_b377bd6f-ba86-4401-84af-a5f6da156fa6_Input-Variables-httpStatus"/>
          <types:variable name="fileInfo" type="auto_b377bd6f-ba86-4401-84af-a5f6da156fa6_Input-Variables-fileInfo"/>
        </types:variables>
      </types:input-event>
      <types:output-event>
        <types:message>
          <types:payload type="auto_b377bd6f-ba86-4401-84af-a5f6da156fa6_Output-Payload"/>
          <types:attributes type="auto_b377bd6f-ba86-4401-84af-a5f6da156fa6_Output-Attributes"/>
        </types:message>
        <types:variables>
          <types:variable name="sfErrorResponse" type="auto_b377bd6f-ba86-4401-84af-a5f6da156fa6_Output-Variables-sfErrorResponse"/>
          <types:variable name="isLeadConverted" type="auto_b377bd6f-ba86-4401-84af-a5f6da156fa6_Output-Variables-isLeadConverted"/>
          <types:variable name="outboundHeaders" type="auto_b377bd6f-ba86-4401-84af-a5f6da156fa6_Output-Variables-outboundHeaders"/>
          <types:variable name="opportunityId" type="auto_b377bd6f-ba86-4401-84af-a5f6da156fa6_Output-Variables-opportunityId"/>
          <types:variable name="isCsvFileCreated" type="auto_b377bd6f-ba86-4401-84af-a5f6da156fa6_Output-Variables-isCsvFileCreated"/>
          <types:variable name="httpStatus" type="auto_b377bd6f-ba86-4401-84af-a5f6da156fa6_Output-Variables-httpStatus"/>
          <types:variable name="sfOpportunityLineItemsResponse" type="auto_b377bd6f-ba86-4401-84af-a5f6da156fa6_Output-Variables-sfOpportunityLineItemsResponse"/>
          <types:variable name="fileInfo" type="auto_b377bd6f-ba86-4401-84af-a5f6da156fa6_Output-Variables-fileInfo"/>
        </types:variables>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="auto_5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c_Input-Payload"/>
          <types:attributes type="auto_5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c_Input-Attributes"/>
        </types:message>
        <types:variables>
          <types:variable name="outboundHeaders" type="auto_5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c_Input-Variables-outboundHeaders"/>
          <types:variable name="isCsvFileCreated" type="auto_5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c_Input-Variables-isCsvFileCreated"/>
          <types:variable name="sfOpportunityLineItemsResponse" type="auto_5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c_Input-Variables-sfOpportunityLineItemsResponse"/>
          <types:variable name="fileInfo" type="auto_5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c_Input-Variables-fileInfo"/>
        </types:variables>
      </types:input-event>
      <types:output-event>
        <types:message>
          <types:payload type="auto_5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c_Output-Payload"/>
          <types:attributes type="auto_5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c_Output-Attributes"/>
        </types:message>
        <types:variables>
          <types:variable name="pricebookEntryResponse" type="auto_5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c_Output-Variables-pricebookEntryResponse"/>
          <types:variable name="sfPricebookEntryResponse" type="auto_5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c_Output-Variables-sfPricebookEntryResponse"/>
          <types:variable name="pricebookEntry" type="auto_5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c_Output-Variables-pricebookEntry"/>
          <types:variable name="sfOpportunityLineItemsResponse" type="auto_5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c_Output-Variables-sfOpportunityLineItemsResponse"/>
          <types:variable name="fileInfo" type="auto_5f229e29-b1ef-4c30-b5d4-dd1afa6bd34c_Output-Variables-fileInfo"/>
        </types:variables>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#aa247255-5126-42db-946f-f486990977e7">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
      <types:output-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#413132fc-fc5e-42d8-8f75-babfb2523476">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="auto_413132fc-fc5e-42d8-8f75-babfb2523476_Input-Payload"/>
          <types:attributes type="auto_413132fc-fc5e-42d8-8f75-babfb2523476_Input-Attributes"/>
        </types:message>
        <types:variables>
          <types:variable name="sfErrorResponse" type="auto_413132fc-fc5e-42d8-8f75-babfb2523476_Input-Variables-sfErrorResponse"/>
          <types:variable name="sfOpportunityResponse" type="auto_413132fc-fc5e-42d8-8f75-babfb2523476_Input-Variables-sfOpportunityResponse"/>
          <types:variable name="httpStatus" type="auto_413132fc-fc5e-42d8-8f75-babfb2523476_Input-Variables-httpStatus"/>
          <types:variable name="sfAccountResponse" type="auto_413132fc-fc5e-42d8-8f75-babfb2523476_Input-Variables-sfAccountResponse"/>
          <types:variable name="accountRequest" type="auto_413132fc-fc5e-42d8-8f75-babfb2523476_Input-Variables-accountRequest"/>
        </types:variables>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#7d331a03-4b4b-4e96-97c9-c83b9ada9ac0">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#32c80838-46e9-451f-8671-7c4c87991c77">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#61c76c2f-2f5d-4093-89f4-448524ad1d3a">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#18d5778f-d230-47a3-9ecf-6faea61f134d">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#ac00395c-a6df-4fd6-9338-e56b50e5720a">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#99793dd3-f99c-441c-844a-927ccba1d07b">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#3a426dc7-9d79-4529-86c1-cefb9c8b43fb">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#be0b2231-4965-4c82-9570-529eeefe883c">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#1eb3e915-3274-43d3-9c7f-07bef70d59ca">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#ef5583c1-d7ad-458a-96be-e76c612230d6">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#201f76c6-0294-4e90-8d0e-1cb5d4a6426b">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#d0d136a5-a337-4257-b77b-877cfe22e28d">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="sfContactEmailRequest"/>
        </types:message>
      </types:input-event>
      <types:output-event>
        <types:variables>
          <types:variable name="sfErrorResponse" type="sfContactEmailNotFoundErrorResponse"/>
        </types:variables>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#0220e8b1-f82c-4d41-8868-2c41a527b1a3">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="sfContactEmailRequest"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#1859e427-0930-4c95-885d-9e1ea3549732">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="OutboundErrorResponse"/>
        </types:message>
        <types:variables>
          <types:variable name="errorDescription" type="OutboundErrorResponse"/>
        </types:variables>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#1afde962-0ed3-40d6-9b69-84ce46122704">
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="sfContactEmailErrorResponse"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#4dee7515-902a-4e93-918a-0fd967ea112c">
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="sfContactDomainAccountsErrorResponse"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#5c997a81-1e14-4c4c-8e99-410a4b95f37b">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="erpContactEmailDomainAccountsResponse"/>
        </types:message>
      </types:input-event>
      <types:output-event>
        <types:message>
          <types:payload type="erpContactEmailDomainAccountsResponse"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#09bbd59e-407a-4e15-b170-f28aeeb836e0">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#fa60d3ea-aca7-4e87-b573-8a73351ca251">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="auto_fa60d3ea-aca7-4e87-b573-8a73351ca251_Input-Payload"/>
          <types:attributes type="auto_fa60d3ea-aca7-4e87-b573-8a73351ca251_Input-Attributes"/>
        </types:message>
        <types:variables>
          <types:variable name="leadRequest" type="auto_fa60d3ea-aca7-4e87-b573-8a73351ca251_Input-Variables-leadRequest"/>
          <types:variable name="httpStatus" type="auto_fa60d3ea-aca7-4e87-b573-8a73351ca251_Input-Variables-httpStatus"/>
          <types:variable name="entityInfo" type="auto_fa60d3ea-aca7-4e87-b573-8a73351ca251_Input-Variables-entityInfo"/>
          <types:variable name="contactRequest" type="auto_fa60d3ea-aca7-4e87-b573-8a73351ca251_Input-Variables-contactRequest"/>
          <types:variable name="accountRequest" type="auto_fa60d3ea-aca7-4e87-b573-8a73351ca251_Input-Variables-accountRequest"/>
        </types:variables>
      </types:input-event>
      <types:output-event>
        <types:message>
          <types:payload type="auto_fa60d3ea-aca7-4e87-b573-8a73351ca251_Output-Payload"/>
          <types:attributes type="auto_fa60d3ea-aca7-4e87-b573-8a73351ca251_Output-Attributes"/>
        </types:message>
        <types:variables>
          <types:variable name="outboundHeaders" type="auto_fa60d3ea-aca7-4e87-b573-8a73351ca251_Output-Variables-outboundHeaders"/>
          <types:variable name="leadRequest" type="auto_fa60d3ea-aca7-4e87-b573-8a73351ca251_Output-Variables-leadRequest"/>
          <types:variable name="salesforceError" type="auto_fa60d3ea-aca7-4e87-b573-8a73351ca251_Output-Variables-salesforceError"/>
          <types:variable name="sfContentVersionResponse" type="auto_fa60d3ea-aca7-4e87-b573-8a73351ca251_Output-Variables-sfContentVersionResponse"/>
          <types:variable name="httpStatus" type="auto_fa60d3ea-aca7-4e87-b573-8a73351ca251_Output-Variables-httpStatus"/>
          <types:variable name="entityInfo" type="auto_fa60d3ea-aca7-4e87-b573-8a73351ca251_Output-Variables-entityInfo"/>
          <types:variable name="contactRequest" type="auto_fa60d3ea-aca7-4e87-b573-8a73351ca251_Output-Variables-contactRequest"/>
          <types:variable name="accountRequest" type="auto_fa60d3ea-aca7-4e87-b573-8a73351ca251_Output-Variables-accountRequest"/>
          <types:variable name="sfContentDocumentLinkResponse" type="auto_fa60d3ea-aca7-4e87-b573-8a73351ca251_Output-Variables-sfContentDocumentLinkResponse"/>
        </types:variables>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#0970ae0a-eb66-42da-ad5a-799ca5d8dd10">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#ba8177b0-5036-47af-914d-78aa9e5ab8ef">
    <types:processor-declaration>
      <types:output-event>
        <types:variables>
          <types:variable name="artInsertRequest" type="artInsertRequest"/>
        </types:variables>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#0e3c1878-b5dd-4277-99bb-46a4e2ae0dfa">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="OutboundErrorResponse"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#dd7e91d7-f1ce-41e5-9f44-f44d3a6ccc2d">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="OutboundErrorResponse"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#d6861ea4-4d30-42d9-90ae-4e7db4aabba5">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="OutboundErrorResponse"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#8fd41d00-4c89-4534-9bb3-4f20a4e0bb59">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="OutboundErrorResponse"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#532cffb5-2625-4d58-b039-ea27fb8b6fce">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="OutboundErrorResponse"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#12c7dd72-fab7-43ea-b18e-54a34e357575">
    <types:processor-declaration>
      <types:output-event>
        <types:variables>
          <types:variable name="artErrorResponse" type="artErrorResponse"/>
        </types:variables>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#73b92f5a-697f-4391-9706-7f3b4e285578">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#7f317319-574e-4f73-9051-49aabae7db5a">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="sfContactEmailRequest"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#6ab5208b-a1f0-456b-b583-736f27746a64">
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="value" type="sfContactCreateErrorResponse"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#010af2bd-dbdc-49ff-a916-b1d781388573">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#f40b0798-e062-4cce-af74-abde8929d151">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#e9bf732f-8996-4214-ba89-a687ff991572">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#ab16c50b-c5e8-4003-8574-ba2d856523c8">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#a7d84363-103a-4b8e-90aa-b3b092a7327b">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#cfb8b2dc-0e69-46ea-8a2e-1b7cbe21be6c">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#97a1878a-49ea-4b87-8ea9-c2efe8d571fe">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#256547f1-9b4b-4d60-975f-cc684cd69aef">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="sfAuthAccessTokenResponse"/>
        </types:message>
      </types:input-event>
      <types:output-event>
        <types:message>
          <types:payload type="sfAuthTokenRefreshResponse"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#fa277c72-0408-42ed-95e2-c4602a4b841a">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="PAYLOAD_String"/>
        </types:message>
      </types:input-event>
      <types:output-event>
        <types:message>
          <types:payload type="PAYLOAD_String"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#b80eecfe-0b2e-492f-9009-480d66cce302">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="sfApiErrorResponse"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#b5997191-020f-4553-b727-9fe4bc396d41">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="sfApiErrorResponse"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#18f840f7-94c4-4794-859c-620190a4bf97">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="sfApiErrorResponse"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#5d35e7c6-40b6-4f0a-9c9b-24fc1612cff7">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="sfApiErrorResponse"/>
        </types:message>
      </types:input-event>
      <types:output-event>
        <types:message>
          <types:payload type="OutboundErrorResponse"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#4803b6eb-d2b7-4a5f-9d95-fec2cbf9c00d">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="sfApiErrorResponse"/>
        </types:message>
      </types:input-event>
      <types:output-event>
        <types:message>
          <types:payload type="OutboundErrorResponse"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#0d5957d8-8246-49b8-9d10-dd0d9613a472">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="PAYLOAD_String"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
    <types:operation-declaration>
      <types:inputs>
        <types:parameter name="body" type="sfAuthRequestBody"/>
      </types:inputs>
    </types:operation-declaration>
  </types:enrichment>
  <types:enrichment select="#e0e5ef5f-ebad-41be-ac9a-e3d31eb3ddbf">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
      <types:output-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#02f51cfb-67c7-48ed-b84b-719602778dc1">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="sfLeadConvertedResponse"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#582641ce-9459-468a-8d02-ca2b724215ea">
    <types:processor-declaration>
      <types:output-event>
        <types:variables>
          <types:variable name="objectStore" type="auto_582641ce-9459-468a-8d02-ca2b724215ea_Output-Variables-objectStore"/>
        </types:variables>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#********-fd7e-43fd-b257-34a308d21c50">
    <types:processor-declaration>
      <types:output-event>
        <types:message>
          <types:payload type="auto_********-fd7e-43fd-b257-34a308d21c50_Output-Payload"/>
          <types:attributes type="auto_********-fd7e-43fd-b257-34a308d21c50_Output-Attributes"/>
        </types:message>
      </types:output-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#9d8c6c96-60ce-4fc3-ab03-133949da8c35">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#c80d7b69-c818-4efd-8e5d-5eb17453a212">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#8e32c6d6-fb66-49fb-91fd-ffca7ac9e4f3">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
  <types:enrichment select="#8095e072-7a61-4a9e-bd73-537917a95ec7">
    <types:processor-declaration>
      <types:input-event>
        <types:message>
          <types:payload type="salesforcePurchaseRequest"/>
        </types:message>
      </types:input-event>
    </types:processor-declaration>
  </types:enrichment>
</types:mule>