<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd">
	<sub-flow name="salesforce_errors_lead_upsert" doc:id="a3cb7fd2-1bbd-4e69-bec1-71ec866b7054" >
		<logger level="INFO" doc:name="LOG INFO: Flow Start" doc:id="58c88043-28a1-4ebd-979b-36cc8a631017" message='#[output application/json&#10;---&#10;{&#10;	message: "FLOW START",&#10;	flowName: "salesforce_errors_lead_upsert",&#10;	correlationId: correlationId&#10;}]' category="flows"/>
		<set-variable value="#[%dw 2.0&#10;var request = vars.leadRequest&#10;var response = vars.sfLeadResponse&#10;output application/java&#10;---&#10;{&#10;	Lead: {&#10;		Id: request.Id,&#10;		Name: request.Name,&#10;		Company: request.Company,&#10;		Email: request.Email&#10;	},&#10;	Success: response.successful,&#10;	Errors: response.items map() -&gt; {&#10;		StatusCode: $.statusCode,&#10;		Message: &quot;FAILURE: LEAD NOT POSTED TO SALESFORCE&quot;,&#10;		Duplicates: flatten(($..payload..duplicateResult default {&#10;		})..matchResults) map(duplicate) -&gt; {&#10;			EntityType: duplicate.entityType,&#10;			Rule: duplicate.rule,&#10;			Size: duplicate.size,&#10;			Records: duplicate.matchRecords map(record) -&gt; {&#10;				Difference: {&#10;					Different: (record.fieldDiffs filter $.difference ~= &quot;DIFFERENT&quot;).name default [],&#10;					Same: (record.fieldDiffs filter $.difference ~= &quot;SAME&quot;).name default []&#10;				},&#10;				(record.record.'type'): record.record - 'type'&#10;			}&#10;		},&#10;		Fields: flatten($..payload..fields)&#10;	}&#10;}]" doc:name="Error" doc:id="f793daeb-9b81-439e-9392-2fbb3999b8a8" variableName="sfErrorResponse" />
		<logger level="INFO" doc:name="LOG INFO: Flow End" doc:id="26b0e844-ef1b-4cd0-94ad-3bc496daec81" message='#[output application/json&#10;---&#10;{&#10;	message: "FLOW END",&#10;	flowName: "salesforce_errors_lead_upsert",&#10;	correlationId: correlationId&#10;}]' category="flows"/>
	</sub-flow>
	<sub-flow name="salesforce_errors_opportunity_upsert" doc:id="070bef2d-642b-47b7-a415-cdd43c55c77c" >
		<logger level="INFO" doc:name="LOG INFO: Flow Start" doc:id="37955b10-528d-4d99-904a-5e03659e1435" message='#[output application/json&#10;---&#10;{&#10;	message: "FLOW START",&#10;	flowName: "salesforce_errors_opportunity_upsert",&#10;	correlationId: correlationId&#10;}]' />
		<set-variable value="#[%dw 2.0&#10;var request = vars.opportunityRequest&#10;var response = vars.sfOpportunityResponse&#10;output application/java&#10;---&#10;{&#10;	Opportunity: {&#10;		Id: request.Id,&#10;		Name: request.Name&#10;	},&#10;	Success: response.successful,&#10;	Errors: response.items map() -&gt; {&#10;		StatusCode: $.statusCode,&#10;		Message: &quot;FAILURE: OPPORTUNITY NOT POSTED TO SALESFORCE&quot;,&#10;		Duplicates: flatten(($..payload..duplicateResult default {&#10;		})..matchResults) map(duplicate) -&gt; {&#10;			EntityType: duplicate.entityType,&#10;			Rule: duplicate.rule,&#10;			Size: duplicate.size,&#10;			Records: duplicate.matchRecords map(record) -&gt; {&#10;				Difference: {&#10;					Different: (record.fieldDiffs filter $.difference ~= &quot;DIFFERENT&quot;).name default [],&#10;					Same: (record.fieldDiffs filter $.difference ~= &quot;SAME&quot;).name default []&#10;				},&#10;				(record.record.'type'): record.record - 'type'&#10;			}&#10;		},&#10;		Fields: flatten($..payload..fields)&#10;	}&#10;}]" doc:name="Error" doc:id="afa6adf2-2967-4bad-98bd-8b17ee0f267e" variableName="sfErrorResponse" />
		<logger level="INFO" doc:name="LOG INFO: Flow End" doc:id="5269cf83-426f-45e3-8150-be6908170332" message='#[output application/json&#10;---&#10;{&#10;	message: "FLOW END",&#10;	flowName: "salesforce_errors_opportunity_upsert",&#10;	correlationId: correlationId&#10;}]' />
	</sub-flow>
</mule>
