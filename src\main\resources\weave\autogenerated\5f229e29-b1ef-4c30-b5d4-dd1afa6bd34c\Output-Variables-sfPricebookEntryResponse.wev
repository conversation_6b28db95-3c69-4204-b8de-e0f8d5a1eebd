%dw 2.0

type auto_5f229e29_b1ef_4c30_b5d4_dd1afa6bd34c_Output_Variables_sfPricebookEntryResponse = Array<{|  attributes*: Null,   payload*: PricebookEntry {"label": "Price Book Entry",
  "typeId": "PricebookEntry"}|}>
type PricebookEntry = {|
  Id?: String {"typeId": "Id"}, 
  Product2?: Array<{|    Climate_Action_Portal_Product_ID__c?: String {"typeId": "Climate_Action_Portal_Product_ID__c"},     Name?: String {"typeId": "Name"},     ProductCode?: String {"typeId": "ProductCode"}  |}>, 
  Product2Id?: String, 
  Vintage__c?: String {"typeId": "Vintage__c"}
|} {"label": "Price Book Entry",
"typeId": "PricebookEntry"}



