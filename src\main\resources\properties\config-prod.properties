# > ANYPOINT PLATFORM
api.version=v1
mule.env=prod
mule.encoding=UTF-8

# > ARTEMIS API 
artemis.api.https.request.host=artemisapi.luna.local
artemis.api.https.request.basePath=/api
artemis.api.https.request.query.database=Artemis
artemis.api.https.request.query.schema=dbo
artemis.api.https.request.query.insert.objectName=tdp_Web_API_Insert

# > SALESFORCE
salesforce.audience.url=https://login.salesforce.com
salesforce.token.endpoint=https://3degrees.my.salesforce.com/services/oauth2/token
salesforce.authorizationUrl=https://3degrees.my.salesforce.com/services/oauth2/authorize

# NETSUITE
netsuite.retry.maxRetries=3
netsuite.retry.timePeriod=1000
netsuite.user=<EMAIL>
netsuite.wsdlVersion=V2021_1
netsuite.soapPort=services/NetSuitePort_2021_1
netsuite.signatureAlgorithm=HMAC_SHA256
netsuite.read.timeout=175000
netsuite.connection.timeout=60000
netsuite.reconnection.frequency=10000
netsuite.reconnection.attempts=3
netsuite.smtp.user=<EMAIL>

# > XERO
xero.base.uri=https://api.xero.com/api.xro/2.0
xero.authorization.url=https://login.xero.com/identity/connect/authorize
xero.authorization.path=/authorize
xero.token.url=https://identity.xero.com/connect/token
xero.callback.url=https://xero-sys-api-kyc100.lnjust.usa-w2.cloudhub.io/callback
xero.callback.path=/callback
xero.scopes=email openid profile accounting.transactions accounting.transactions.read accounting.reports.read accounting.reports.tenninetynine.read accounting.journals.read accounting.settings accounting.settings.read accounting.contacts accounting.contacts.read accounting.attachments accounting.attachments.read assets assets.read bankfeeds files files.read payroll payroll.read payroll.employees payroll.employees.read payroll.leaveapplications payroll.leaveapplications.read payroll.payitems payroll.payitems.read payroll.payrollcalendars payroll.payrollcalendars.read payroll.payruns payroll.payruns.read payroll.payslip payroll.payslip.read payroll.settings.read payroll.superfunds payroll.superfunds.read payroll.superfundproducts.read payroll.timesheets payroll.timesheets.read paymentservices projects projects.read
xero.connection.timeout=30000
xero.connection.timeout.unit=MILLISECONDS
xero.connection.max=-1
xero.connection.idle.timeout=30000
xero.connection.idle.timeout.unit=MILLISECONDS
xero.reconnection.frequency=1000
xero.reconnection.attempts=3
xero.response.timeout=60000
xero.response.timeout.unit=MILLISECONDS
xero.idle.time.max=30000
xero.idle.time.unit=MILLISECONDS

# > SMB: R:\ATLANTIS
smb.r.atlantis.path=server2.luna.local\atlantis
smb.r.atlantis.file.age=1
smb.r.atlantis.encoding=UTF-8
smb.r.atlantis.idle.timeout=30000
smb.r.atlantis.idle.timeout.unit=SECONDS
smb.r.atlantis.connection.timeout=30
smb.r.atlantis.connection.timeout.unit=SECONDS
smb.r.atlantis.socket.timeout=0
smb.r.atlantis.socket.timeout.unit=SECONDS
smb.r.atlantis.reconnection.frequency=2000
smb.r.atlantis.reconnection.attempts=2