<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd">
		<flow name="util_remove_variables" doc:id="1848d8a7-a8d5-4f76-8824-143ad3be4997" initialState="started">
		<logger level="INFO" doc:name="LOG INFO: Flow Start" doc:id="e8b5f2c7-6508-4af7-b92c-4c1c1ad41d2d" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW START",&#10;	Flow: "common\common.util\\util-variables\\util_remove_variables",&#10;	CorrelationID: correlationId&#10;}]' />
		<foreach doc:name="For Each Variable" doc:id="1086187f-9235-4e63-9d00-d5c08f0fa7b2" collection="#[vars.variables]">
			<remove-variable doc:name="Remove Variable" doc:id="8c62d733-b1e0-4b42-a6df-ea765fcc9f71" variableName="#[payload]" />
			<logger level="INFO" doc:name="LOG INFO: Variable Removed" doc:id="c8aeab68-20d4-4664-98b6-551c63b1fef3" message='#[output application/json&#10;---&#10;{&#10;	Message: "SUCCESS: VARIABLE REMOVED",&#10;	Variable: payload&#10;}]' />
		</foreach>
		<remove-variable doc:name="Variables" doc:id="d7c4c67a-1720-4ac3-a402-f20bce519b4f" variableName="variables"/>
		<logger level="INFO" doc:name="LOG INFO: Flow End" doc:id="19b1b7eb-ff31-47f2-bcf9-54cd070cf950" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW END",&#10;	Flow: "common\commont.util\\util-variables\\util_remove_variables",&#10;	CorrelationID: correlationId&#10;}]' />
	</flow>
	
	</mule>
