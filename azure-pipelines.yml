variables:
- group: IT-Variable-Group-Prod
- name: MAVEN_CACHE_FOLDER
  value: $(Pipeline.Workspace)/.m2/repository
- name: MAVEN_OPTS
  value: '-Dmaven.repo.local=$(MAVEN_CACHE_FOLDER)'
  
trigger:
  batch: true
  branches:
    include:
    - main

  paths:
    exclude:
    - azure-pipelines.yml
    - pom.xml

pr: none
    
pool:
  vmImage: 'ubuntu-latest'
  
# stages:
# - stage: 'Deployment'
#   jobs:
#   - deployment: 'DeployToDev'
#     displayName: "Deploy to Dev"
#     environment: $(checkout.branch)
#     strategy:
#       runOnce:
#         deploy:

steps:
  - checkout: self
    path: $(checkout.branch)
  - task: DownloadSecureFile@1
    displayName: Download settings file
    name: settingsxml
    inputs:
      secureFile: 'settings-prod.xml'
  - task: PowerShell@2
    displayName: Copy settings file to .m2 folder
    inputs:
      targetType: 'inline'
      script: |
        New-Item -Type Directory -Force "${HOME}/.m2"
        Copy-Item -Force "$(settingsxml.secureFilePath)" "${HOME}/.m2/settings.xml"
  - task: Maven@3
    displayName: Run munit test    
    inputs:
      mavenPomFile: 'pom.xml'
      mavenOptions: '-Xmx3072m'
      jdkVersionOption: '1.8'
      jdkArchitectureOption: 'x64'
      mavenAuthenticateFeed: true
      publishJUnitResults: false
      javaHomeOption: 'JDKVersion'
      effectivePomSkip: true
      mavenVersionOption: 'Default'
      sonarQubeRunAnalysis: false
      goals: 'clean test -Dmule.env=$(mule.env) -Dmule.key=$(mule.key)'
  - task: Maven@3
    displayName: Set build version    
    inputs:
      mavenPomFile: 'pom.xml'
      mavenOptions: '-Xmx3072m'
      jdkVersionOption: '1.8'
      jdkArchitectureOption: 'x64'
      mavenAuthenticateFeed: true
      publishJUnitResults: false
      javaHomeOption: 'JDKVersion'
      mavenVersionOption: 'Default'
      effectivePomSkip: true
      sonarQubeRunAnalysis: false
      goals: 'versions:set -DremoveSnapshot=true'

  - task: Maven@3
    displayName: Deploy to runtime manager    
    inputs:
      mavenPomFile: 'pom.xml'
      mavenOptions: '-Xmx3072m'
      jdkVersionOption: '1.8'
      jdkArchitectureOption: 'x64'
      mavenAuthenticateFeed: true
      publishJUnitResults: false
      javaHomeOption: 'JDKVersion'
      mavenVersionOption: 'Default'
      effectivePomSkip: true
      sonarQubeRunAnalysis: false
      goals: 'deploy -DmuleDeploy -DskipTests=true -Dserver=$(server) -Denvironment=$(environment) -Dtarget=$(target.name) -DbusinessGroupId=$(mule.deploy.business.group) -Dapp.runtime=$(app.runtime) -Dapp.name=$(app.name) -Dreplicas=$(app.replicas) -DvCores=$(app.vcores) -Danypoint.platform.client_id=$(anypoint.platform.client_id) -Danypoint.platform.client_secret=$(anypoint.platform.client_secret) -Dmule.env=$(mule.env) -Dmule.key=$(mule.key) -DgenerateDefaultPublicUrl=$(generateDefaultPublicUrl) -Dclustered=$(clustered) -DskipDeploymentVerification=$(skipDeploymentVerification)'
