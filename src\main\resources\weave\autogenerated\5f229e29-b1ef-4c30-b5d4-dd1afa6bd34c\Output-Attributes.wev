%dw 2.0

type auto_5f229e29_b1ef_4c30_b5d4_dd1afa6bd34c_Output_Attributes = {|
  clientCertificate*?: {|
    publicKey?: {|  |}, 
    "type"?: String, 
    encoded?: Binary
  |}, 
  headers*: {|  |}, 
  listenerPath*: String, 
  localAddress*: String, 
  method*: String, 
  queryParams*: {|
    folderPath: String, 
    fileName: String, 
    entity: String
  |}, 
  queryString*: String, 
  relativePath*: String, 
  remoteAddress*: String, 
  requestPath*: String, 
  requestUri*: String, 
  scheme*: String, 
  uriParams*: {|  |}, 
  version*: String
|}

