# ANYPOINT PLATFORM
api.autodiscovery.id=![PdYcBd3ZJEWliFSgU0rrFQ==]

# > HTTPS
https.listener.keystore.path=![A0Uc6EIyiTtbeLX519OIAYfN2GEd5g0z]
https.listener.keystore.password=![0ZwfIP8jWN1hmltBHTlY1w==]
https.listener.keystore.keyPassword=![0ZwfIP8jWN1hmltBHTlY1w==]
https.listener.keystore.alias=![Hw1kFt76WUyGDQoU5rnAKvhbgx4f/77r]
https.listener.truststore.path=![Mz/IpoaXUudea2gGk5KSg+3fMmYhSCzC]
https.listener.truststore.password=![0ZwfIP8jWN1hmltBHTlY1w==]

# ARTEMIS API
artemis.api.tls.truststore.password=![0ZwfIP8jWN1hmltBHTlY1w==]
artemis.api.https.request.headers.clientId=![9cFQIMsdKB6KBpHW4OKEvQ==]
artemis.api.https.request.headers.clientSecret=![Nxp+ATSsGfX8cmIeCHJoiTSCiFG047KwDoIeGw5BXB4/Pw069W3l0A==]

# SALESFORCE 
salesforce.oauth.jwt.consumerKey=![1M4Zyp1/iehw05mOkQ6dXnul0x74SA3YN1Rk3LPHNooxdjci5+WXwNlQwu6TpXOeRlA9MmSKSHwdRySyWmCc7yRyrHgLtgsnY8yEX5/+zupnqxQT0D8Twg==]
salesforce.oauth.jwt.keystore.password=![0ZwfIP8jWN1hmltBHTlY1w==]
salesforce.oauth.jwt.certificate.alias=![Hw1kFt76WUyGDQoU5rnAKvhbgx4f/77r]
salesforce.keystore.path=![79Gu0GKxt9eIAFZb7RB3IVA9whqirOLb]
salesforce.user.mulesoft.id=![58OuOcmqWlEZN+fAYQwaq0Z9SQIU5qBD]
salesforce.principal=![vZQKgNPL0VWSRCZS8C1GNOpSreEAZsiZ9vfKED490A3KAhXBtHPcViC3MwdQweFDZRWpDStyPLY=]

# SALESFORCE: Supplier REach
salesforce.supplierREach.lead.leadSource=![DocGPvNba6j+GWdLxd8jbw==]
salesforce.supplierREach.lead.recordType.id=![7pQw1Ryr65QwPZ+2Ft6OoPcOuQpYEjjk]
salesforce.supplierREach.lead.status=![s5fFPN9lpn5oD7C276zcWw==]
salesforce.supplierREach.lead.product=![DocGPvNba6hxOJUOQ2p9AQ==]
salesforce.supplierREach.lead.owner.id=![5ceHpAhlFTAJpW2NbGpw+F7qo06QweXw]
salesforce.supplierREach.folderPath=![m1HVFRyg2dgNAKxgtF2Dtvz85pc6kZUJ]

# SALESFORCE: Marketplace
salesforce.marketplace.lead.leadSource=![fQ5Ou8/JBbR7XlS3x0RqAw==]
salesforce.marketplace.lead.recordType.id=![7pQw1Ryr65QwPZ+2Ft6OoPcOuQpYEjjk]
salesforce.marketplace.lead.status=![s5fFPN9lpn5oD7C276zcWw==]
salesforce.marketplace.lead.owner.id=![5ceHpAhlFTAJpW2NbGpw+F7qo06QweXw]
salesforce.marketplace.account.accountSource=![fQ5Ou8/JBbR7XlS3x0RqAw==]
salesforce.marketplace.account.owner.id=![5ceHpAhlFTAJpW2NbGpw+F7qo06QweXw]
salesforce.marketplace.contact.owner.id=![5ceHpAhlFTAJpW2NbGpw+F7qo06QweXw]
salesforce.marketplace.opportunity.stageName=![9nl7I2u/SiRxVxcteFMDiw==]
salesforce.marketplace.opportunity.leadSource=![fQ5Ou8/JBbR7XlS3x0RqAw==]
salesforce.marketplace.opportunity.recordType.id=![nxcqzKIzrFzoFVZfd71K9+nWHpjdtwbF]
salesforce.marketplace.opportunity.pricebook2.id=![NW8sEeHQj0W4jefckQjyEHoQEMg165s8]
salesforce.marketplace.opportunity.onlineTransactions=![4a095DcSxw8=]
salesforce.marketplace.opportunity.owner.id=![5ceHpAhlFTAJpW2NbGpw+F7qo06QweXw]

# XERO
xero.consumerKey=![XV+SglAxZEZqzOI+kxSs6NR4VnY1dL1fsva213RH6WGkfsnofAL0tQ==]
xero.consumerSecret=![0AB7L4vwgHYbKo9vaGJdudcoHWTFJqsiptXY0jvaw5/QHBlFf56FsJ7jtQzC+faK44PTDwL456c=]
xero.tenantId=![kCmzJMT3IHYo8NrAg56EXFQF3HpWtloHT2f2jWXWglFoVWGyj8OFlQ==]
xero.webhook.key=![QjbutT4yWIuXdAqZb7iidqEjykkag0mwY+vqj6QevF67izRAUX5zhE36WF6M9J441KFP16VMegsHFqMeCk/Ae4KovgGxA+4eXORBgBpHlt/S5Z1jKkvUXl4baj6f2lgF]

# SMB: R:\ATLANTIS
smb.r.atlantis.domain=![9Dxl2Kqd2B3Xv6o9ZKQodg==]
smb.r.atlantis.host=![ybjDDsGK2S1be+U//m9ZlA==]
smb.r.atlantis.share=![BFMGlHJvt91tfPPJhtBzdA==]
smb.r.atlantis.share.path=![FNrSzh9u2VNXzHwoPicqnraWuVZk0ISkQEeipP4RkII=]
smb.r.atlantis.server.path=![TwciXICtIwR3RJgXsPOuFWrrCW2ayCsrMGdG1Z+RCGE=]
smb.r.atlantis.username=![84Yy1Dq1IWY67jsPKkQXzA==]
smb.r.atlantis.password=![ktswdt1h+mY=] #7oFBS3xX!JdHq
