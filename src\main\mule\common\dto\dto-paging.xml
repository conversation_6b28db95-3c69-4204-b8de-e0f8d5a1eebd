<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core" xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd">
	<sub-flow name="paging" doc:id="f6d66eff-b075-4c8c-9035-9d9b6fce7717" >
		<logger level="INFO" doc:name="LOG INFO: Flow Start" doc:id="f797b7e2-38db-49cb-9d87-b660e868fe7f" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW START",&#10;	FlowName: "paging",&#10;	CorrelationID: vars.request.correlationID&#10;}]' />
		<ee:transform doc:name="payload" doc:id="3b61f79b-f2dc-412e-b1ad-ea5c01201ead" >
			<ee:message >
				<ee:set-payload ><![CDATA[%dw 2.0
output application/java
---
{
	total: sizeOf(payload),
	pages: 0,
	pageSize: attributes.queryParams.pageSize,
	page: attributes.queryParams.pageNumber
}]]></ee:set-payload>
			</ee:message>
		</ee:transform>
		<logger level="INFO" doc:name="LOG INFO: Flow End" doc:id="e8d507d8-8ed2-4bbb-b706-cf1b9945c9cb" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW END",&#10;	FlowName: "paging",&#10;	CorrelationID: vars.request.correlationID&#10;}]' />
	</sub-flow>
</mule>
