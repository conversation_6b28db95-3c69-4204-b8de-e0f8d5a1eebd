# ANYPOINT PLATFORM
os.load=true

# > HTTP: Listener
http.listener.host=0.0.0.0
http.listener.port=8081
http.listener.basePath=/
http.listener.read.timeout=30000
http.listener.idle.timeout=30000

# > HTTP: Request
http.request.host=0.0.0.0
http.request.port=80
http.request.read.timeout=30000
http.request.idle.timeout=30000

# > HTTPS: Listener
https.listener.host=0.0.0.0
https.listener.port=8081
https.listener.basePath=/
https.listener.read.timeout=30000
https.listener.idle.timeout=40000

# > HTTPS: Request
https.request.host=0.0.0.0
https.request.port=443
https.request.read.timeout=30000
https.request.idle.timeout=30000

# > ARTEMIS API 
artemis.api.https.request.port=443
artemis.api.https.request.connection.max=-1
artemis.api.https.request.connection.timeout=30000
artemis.api.https.request.reconnection.frequency=10000
artemis.api.https.request.reconnection.attempts=3
artemis.api.https.request.headers.accept=text/plain
artemis.api.https.request.response.timeout=30000
artemis.api.tls.truststore.path=tls/truststore-dev.jks

# > SALESFORCE
salesforce.authorize.path=/authorize
salesforce.callback.url=https://localhost:8081/callback
salesforce.connection.pool.ttl=30
salesforce.connection.pool.ttl.unit=SECONDS
salesforce.connection.pool.maxEntries=10
salesforce.connection.timeout=0
salesforce.connection.timeout.unit=SECONDS
salesforce.login.request.timeout=0
salesforce.login.request.timeout.unit=SECONDS
salesforce.reconnection.frequency=10000

# > APIKIT: SUCCESS
apikit.success.ok.code=200
apikit.success.ok.description=OK
apikit.success.created.code=201
apikit.success.created.description=CREATED

# > APIKIT: ERRORS
apikit.errors.badRequest.code=400
apikit.errors.badRequest.description=BAD REQUEST
apikit.errors.forbidden.code=401
apikit.errors.forbidden.description=UNAUTHORIZED
apikit.errors.forbidden.code=403
apikit.errors.forbidden.description=FORBIDDEN
apikit.errors.notFound.code=404
apikit.errors.notFound.description=NOT FOUND
apikit.errors.methodNotAllowed.code=405
apikit.errors.methodNotAllowed.description=METHOD NOT ALLOWED
apikit.errors.notAcceptable.code=406
apikit.errors.notAcceptable.description=NOT ACCEPTABLE
apikit.errors.unsupportedMediaType.code=415
apikit.errors.unsupportedMediaType.description=UNSUPPORTED MEDIA TYPE
apikit.errors.internalServerError.code=500
apikit.errors.internalServerError.description=INTERNAL SERVER ERROR
apikit.errors.notImplemented.code=501
apikit.errors.notImplemented.description=NOT IMPLEMENTED
apikit.errors.expression.code=500
apikit.errors.expression.description=DATAWEAVE EXPRESSION ERROR
apikit.errors.default.code=500
apikit.errors.default.description=INTERNAL SERVER ERROR

# > ARTEMIS API
artemis.api.username.prefix=LUNA

# > SMB: R:\ATLANTIS
smb.r.atlantis.file.age=1
smb.r.atlantis.encoding=UTF-8
smb.r.atlantis.idle.timeout=30000
smb.r.atlantis.idle.timeout.unit=SECONDS
smb.r.atlantis.connection.timeout=30
smb.r.atlantis.connection.timeout.unit=SECONDS
smb.r.atlantis.socket.timeout=0
smb.r.atlantis.socket.timeout.unit=SECONDS
smb.r.atlantis.reconnection.frequency=2000
smb.r.atlantis.reconnection.attempts=2
smb.r.atlantis.file.life.days=1

# > XERO
xero.base.uri=https://api.xero.com/api.xro/2.0
xero.authorization.url=https://login.xero.com/identity/connect/authorize
xero.authorization.path=/authorize
xero.token.url=https://identity.xero.com/connect/token
#xero.callback.url=https://xero-sys-api-kyc100.lnjust.usa-w2.cloudhub.io/callback
xero.callback.path=/callback
xero.scopes=email openid profile accounting.transactions accounting.transactions.read accounting.reports.read accounting.reports.tenninetynine.read accounting.journals.read accounting.settings accounting.settings.read accounting.contacts accounting.contacts.read accounting.attachments accounting.attachments.read assets assets.read bankfeeds files files.read payroll payroll.read payroll.employees payroll.employees.read payroll.leaveapplications payroll.leaveapplications.read payroll.payitems payroll.payitems.read payroll.payrollcalendars payroll.payrollcalendars.read payroll.payruns payroll.payruns.read payroll.payslip payroll.payslip.read payroll.settings.read payroll.superfunds payroll.superfunds.read payroll.superfundproducts.read payroll.timesheets payroll.timesheets.read paymentservices projects projects.read
xero.connection.timeout=30000
xero.connection.timeout.unit=MILLISECONDS
xero.connection.max=-1
xero.connection.idle.timeout=30000
xero.connection.idle.timeout.unit=MILLISECONDS
xero.reconnection.frequency=1000
xero.reconnection.attempts=3
xero.response.timeout=60000
xero.response.timeout.unit=MILLISECONDS
xero.idle.time.max=30000
xero.idle.time.unit=MILLISECONDS

# > SUPPLIER_REACH
supplierREach.folderPath=CP_Files\\SupplierREach

# SALESFORCE: DOMAIN ACCOUNT EXCLUSIONS
salesforce.email.domain.account.exclusions='126.com','163.com','21cn.com','alice.it','aliyun.com','aol.com','aol.it','arnet.com.ar','att.net','bell.net','bellsouth.net','bk.ru','blueyonder.co.uk','bol.com.br','bt.com','btinternet.com','charter.net','comcast.net','cox.net','daum.net','earthlink.net','email.com','email.it','facebook.com','fastmail.fm','fibertel.com.ar','foxmail.com','free.fr','games.com','globo.com','globomail.com','gmail.com','gmx.com','gmx.de','gmx.fr','gmx.net','googlemail.com','hanmail.net','hotmail.be','hotmail.ca','hotmail.co.uk','hotmail.com','hotmail.com.ar','hotmail.com.br','hotmail.com.mx','hotmail.de','hotmail.es','hotmail.fr','hotmail.it','hush.com','hushmail.com','icloud.com','ig.com.br','iname.com','inbox.com','inbox.ru','juno.com','keemail.me','laposte.net','lavabit.com','libero.it','list.ru','live.be','live.co.uk','live.com','live.com.ar','live.com.mx','live.de','live.fr','live.it','love.com','mac.com','mail.com','mail.ru','mailinator.com','me.com','msn.com','nate.com','naver.com','neuf.fr','ntlworld.com','oi.com.br','online.de','orange.fr','orange.net','outlook.com','outlook.com.br','pobox.com','poste.it','prodigy.net.mx','protonmail.ch','protonmail.com','qq.com','r7.com','rambler.ru','rocketmail.com','rogers.com','safe-mail.net','sbcglobal.net','sfr.fr','shaw.ca','sina.cn','sina.com','sky.com','skynet.be','speedy.com.ar','sympatico.ca','t-online.de','talktalk.co.uk','telenet.be','teletu.it','terra.com.br','tin.it','tiscali.co.uk','tiscali.it','tuta.io','tutamail.com','tutanota.com','tutanota.de','tvcablenet.be','uol.com.br','verizon.net','virgilio.it','virgin.net','virginmedia.com','voo.be','wanadoo.fr','web.de','wow.com','ya.ru','yahoo.ca','yahoo.co.id','yahoo.co.in','yahoo.co.jp','yahoo.co.kr','yahoo.co.uk','yahoo.com','yahoo.com.ar','yahoo.com.br','yahoo.com.mx','yahoo.com.ph','yahoo.com.sg','yahoo.de','yahoo.fr','yahoo.it','yandex.by','yandex.com','yandex.kz','yandex.ru','yandex.ua','yeah.net','ygm.com','ymail.com','zipmail.com.br','zoho.com'
