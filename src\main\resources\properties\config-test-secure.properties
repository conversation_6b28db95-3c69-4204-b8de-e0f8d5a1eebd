# ANYPOINT PLATFORM
api.autodiscovery.id=![jv3yeNubb/bg4ITQnACnIw==]

# > HTTPS
https.listener.keystore.path=![onJaPIdSh6pojxsh58g1AzfM15YTx9cm]
https.listener.keystore.password=![T8ib8yautRkkXeeikIo8vA==]
https.listener.keystore.keyPassword=![T8ib8yautRkkXeeikIo8vA==]
https.listener.keystore.alias=![jafzadd8UgrwNX/6SfYjJHMLxG4+VEKE]
https.listener.truststore.path=![y/LAXPfcJsPkuXuUrWl3APl3Bvb0MXq9]
https.listener.truststore.password=![T8ib8yautRkkXeeikIo8vA==]

# ARTEMIS API
artemis.api.tls.truststore.password=![T8ib8yautRkkXeeikIo8vA==]
artemis.api.https.request.headers.clientId=![Nxv2KhbD8sxFe4Jn+wuRYQ==]
artemis.api.https.request.headers.clientSecret=![evh3GaD34a/vtl/TkULEQnjudrOvuQmkUsxZOQCNLD3py/y4/O5vGA==]

# ARTEMIS API: Climate Portal
cp.https.request.query.database=![jhUv+Jo4RnQ=]
cp.https.request.query.schema=![9uElIMVYAlY=]
cp.https.request.query.objectType.table=![6tpMZlsGW3s=]
cp.https.request.query.objectType.view=![d5hOSCc8CFg=]
cp.https.request.query.objectType.sp=![3kI1w79JkiPi6zb/a3mrpA==]
cp.https.request.query.objectName.companies=![S3erd6+/TjGbR77GMpKxJA==]
cp.https.request.query.objectName.products=![fjBle+mBYW1CnWKJ+ap9tQ==]
cp.https.request.query.objectName.companies.transactions=![S3erd6+/TjEECLqkbAybKwXSaDhpOBst]
cp.https.request.query.objectName.companies.transactions.commodities=![S3erd6+/TjEECLqkbAybK1NFI6YMwi7MS/jwO5Fy4AmB84iOJXG4Kw==]
cp.https.request.query.objectName.companies.transactions.products=![S3erd6+/TjEECLqkbAybK55bt8U5+hazrxRU4DTLrQk=]
cp.https.request.query.schema.company.documents=![0I7U9stXW0g=]
cp.https.request.query.objectName.company.documents=![+3uxdAGhdHKSgeQvjYD3tA==]
cp.https.request.query.schema.documents=![0I7U9stXW0g=]
cp.https.request.query.objectName.documents=![+3uxdAGhdHIBNrHnvqrGbf2Pgwfo0rJp]
cp.https.request.query.objectName.opportunities=![lUBUwZJufZEDJchA13D7tA==]

# SALESFORCE 
salesforce.oauth.jwt.consumerKey=![ekDf5PpTLr8v3Sj9tlwPSnos4FdyhQxsnvaSyShEn0oFQvVqnkglEUhQTRjEsqFSu4S/HI2HvgzkLDroAty2k/EtB24zJFemUOAQP/QsSWN9BIEfNConBw==]
salesforce.oauth.jwt.keystore.password=![T8ib8yautRkkXeeikIo8vA==]
salesforce.oauth.jwt.certificate.alias=![jafzadd8UgrwNX/6SfYjJHMLxG4+VEKE]
salesforce.keystore.path=![aS04CCfxNF6U+5oj4tTFc3Djfv2NZi7h]
salesforce.user.mulesoft.id=![B1TH5Oe9NwKwaVH/c/jfwrD7dOxwweoT]
salesforce.principal=![a7IwtqiMGYQj5+qw2hS9R12HGUI7Ju9yk5N5fcwVcVHzGgQK+gm1iT8Gqce522vyGRFkBhSW1U0=]

# SALESFORCE: API
salesforce.oauth.jwt.clientId=![ekDf5PpTLr8v3Sj9tlwPSnos4FdyhQxsnvaSyShEn0oFQvVqnkglEUhQTRjEsqFSu4S/HI2HvgzkLDroAty2k/EtB24zJFemUOAQP/QsSWN9BIEfNConBw==]
salesforce.oauth.jwt.clientSecret=![pXHiP5Ru7DLczFOTT/jfcdJUL00njCJZMYIjHU88NS3oCD4wI8GtURukS47YTx+jmQHY05QaHptSpU1OZ23j+PfcgqewhfCU]
salesforce.oauth.jwt.accessToken=![4qzNDJQYnkQMIufNx9AFGVHvyXS0Vc5KOLtx+umv4TNB7349S44tqAuqoBv3iTC7iqlzz/HSkeyXVbDqtIR2M9TUyrItYw7LKe9zqtCPadKivxlcbAYI+Bnj7uFVgFymXu3n/GxfebQBj3CNvGvCBRhTlgIwlXQr]
salesforce.oauth.jwt.refreshToken=![9upsSj7ELLPA9tQsZddEQnAGePsWJvpBHD1n9sPXv9mlQ0SskQvZNC9AFj2CVJXHggpwBuCCwTsGZByvyA2uurm44O0C1zkwVyLatViQVKxNeLfGbhFVSg==]

# SALESFORCE: Supplier REach
salesforce.supplierREach.lead.leadSource=![nJ0eSxExoKHvPreW/ZqhFw==]
salesforce.supplierREach.lead.recordType.id=![vFg+/FJqwTzvnU6/oSThokk5oL+IislZ]
salesforce.supplierREach.lead.status=![WLXuJs//VTM+TkdgMgeIcg==]
salesforce.supplierREach.lead.product=![nJ0eSxExoKHA6/eVSoB9Ow==]
salesforce.supplierREach.lead.owner.id=![TKVqkrhTsqkqpIqDRw85jd1/uP7q0vya]
salesforce.supplierREach.folderPath=![mxoJKH6jznqiTieGfICT/7gLAQz+FEjx]

# SALESFORCE: Marketplace
salesforce.marketplace.lead.leadSource=![lUAgHgqWzSJH4/vcylOVdg==]
salesforce.marketplace.lead.recordType.id=![aACgexDHRcOStdzBFhwPe7bdfvscUa0Y]
salesforce.marketplace.lead.status=![WLXuJs//VTM+TkdgMgeIcg==]
salesforce.marketplace.lead.owner.id=![TKVqkrhTsqkqpIqDRw85jd1/uP7q0vya]
salesforce.marketplace.account.accountSource=![lUAgHgqWzSJH4/vcylOVdg==]
salesforce.marketplace.account.owner.id=![TKVqkrhTsqkqpIqDRw85jd1/uP7q0vya]
salesforce.marketplace.contact.owner.id=![TKVqkrhTsqkqpIqDRw85jd1/uP7q0vya]
salesforce.marketplace.opportunity.stageName=![61g7YaJHpuFghFATg095Rg==]
salesforce.marketplace.opportunity.leadSource=![lUAgHgqWzSJH4/vcylOVdg==]
salesforce.marketplace.opportunity.recordType.id=![vFg+/FJqwTzvnU6/oSThokk5oL+IislZ]
salesforce.marketplace.opportunity.pricebook2.id=![QsofE13dxIsrQeJcYmnxP6UcJmQXeudM]
salesforce.marketplace.opportunity.onlineTransactions=![wrT5vA20Dj8=]
salesforce.marketplace.opportunity.owner.id=![TKVqkrhTsqkqpIqDRw85jd1/uP7q0vya]
salesforce.marketplace.opportunity.updatedById=![B1TH5Oe9NwKwaVH/c/jfwrD7dOxwweoT]

# XERO
xero.consumerKey=![7eLzTHuPkVY9VWXRC2EVhJlsdDIprCTDuXu/d+VnxgPYrUoIHQVDOg==]
xero.consumerSecret=![zi0nB+I3U+Yztz3BtL3ThrL4QPQN9V/qyEoNzngy3l9sOing2hAU5WIU7xQqB1HQX9stM6e7eas=]
xero.tenantId=![poTuHH/xhT3fU8TaD3PKM82qch9juASaV1ypONXExqqsL7Okc4DYdA==]
xero.webhook.key=![gPI2YWTN1tBwe9+/RB851yOHF7b+44V8CUzYixY9mwSSmeIy1V/NbySzWdYTv1C24L24CNvFqlwbNJQxIjZveIFxin/HM7tZs2tp3npnvqICvypKB1Au0hPqN2wtIwU1]

# SMB: R:\ATLANTIS
smb.r.atlantis.domain=![E0TH2Mj8HJFY74KunEgGSA==]
smb.r.atlantis.host=![ZVPyedXam6rqc0pIftI9ZyCFV8fa2MR1]
smb.r.atlantis.share=![W3nH+poaM4thd+3fKGObRQ==]
smb.r.atlantis.share.path=![r+kFYSmIpK9WXmvwe7InArw5b7qJNtxlG3q+LY7GXJs=]
smb.r.atlantis.server.path=![4vYYoy2x5G1YFwY12eco/UCLyTvf/5+/ylPOxtTl+ME=]
smb.r.atlantis.username=![/NOv/wxwV0xbMEDuqS+V5Q==]
smb.r.atlantis.password=![rgKEgm/Ta4DTO+IhOrC4zGLhLSBjspCz]  
