<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd">
	<sub-flow name="salesforce_lead" doc:id="6db9a0bd-7739-4344-847b-8f2d04b57a73" >
		<!-- [STUDIO:"LOG INFO: Flow Start"]<logger level="INFO" doc:name="LOG INFO: Flow Start" doc:id="79523f66-4799-4293-9377-0b6757a32077" message='#[output application/json&#10;&#45;&#45;-&#10;{&#10;	Message: "FLOW START",&#10;	Flow: "common/common.dto/dto-salesforce/salesforce_lead",&#10;	CorrelationId: vars.request.correlationID&#10;}&#93;' /> [STUDIO] -->
		<!-- [STUDIO:"sfLeadPayload"]<set-variable value="#[output application/java&#10;&#45;&#45;-&#10;payload&#93;" doc:name="sfLeadPayload" doc:id="d0998823-5ad4-46b2-8514-4537f3647f3e" variableName="sfLeadPayload" /> [STUDIO] -->
		<!-- [STUDIO:"LOG INFO: Flow End"]<logger level="INFO" doc:name="LOG INFO: Flow End" doc:id="b0fa74c4-60d5-4d58-bf32-674bd1415383" message='#[output application/json&#10;&#45;&#45;-&#10;{&#10;	Message: "FLOW END",&#10;	Flow: "common/common.dto/dto-salesforce/salesforce_lead",&#10;	CorrelationId: vars.request.correlationID&#10;}&#93;' /> [STUDIO] -->
		<logger level="INFO" doc:name="Logger" doc:id="cd74e7f3-0631-471f-8945-e85639d58053" />
	</sub-flow>
	<sub-flow name="salesforce_opportunity" doc:id="c762a63b-7c42-4e74-a93f-4cb08d44286a" >
		<!-- [STUDIO:"LOG INFO: Flow Start"]<logger level="INFO" doc:name="LOG INFO: Flow Start" doc:id="68ad386a-5144-4924-817c-4204b7289656" message='#[output application/json&#10;&#45;&#45;-&#10;{&#10;	Message: "FLOW START",&#10;	Flow: "common/common.dto/dto-salesforce/salesforce_opportunity",&#10;	CorrelationId: vars.request.correlationID&#10;}&#93;' /> [STUDIO] -->
		<!-- [STUDIO:"sfOpportunityPayload"]<set-variable value="#[payload.Opportunity&#93;" doc:name="sfOpportunityPayload" doc:id="32b2c229-5355-4587-90de-e5028daa1681" variableName="sfOpportunityPayload"/> [STUDIO] -->
		<!-- [STUDIO:"LOG INFO: Flow End"]<logger level="INFO" doc:name="LOG INFO: Flow End" doc:id="dfda004c-a255-4bab-aa05-1878470f3bb8" message='#[output application/json&#10;&#45;&#45;-&#10;{&#10;	Message: "FLOW END",&#10;	Flow: "common/common.dto/dto-salesforce/salesforce_opportunity",&#10;	CorrelationId: vars.request.correlationID&#10;}&#93;' /> [STUDIO] -->
		<logger level="INFO" doc:name="Logger" doc:id="869d0de5-023f-4e5d-81fb-bece98084866" />
	</sub-flow>
</mule>
