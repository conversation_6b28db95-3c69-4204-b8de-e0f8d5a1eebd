<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:oauth="http://www.mulesoft.org/schema/mule/oauth"
	xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns:db="http://www.mulesoft.org/schema/mule/db"
	xmlns:vm="http://www.mulesoft.org/schema/mule/vm"
	xmlns:validation="http://www.mulesoft.org/schema/mule/validation"
	xmlns:os="http://www.mulesoft.org/schema/mule/os"
	xmlns:api-gateway="http://www.mulesoft.org/schema/mule/api-gateway"
	xmlns:xero-accounting="http://www.mulesoft.org/schema/mule/xero-accounting"
	xmlns:email="http://www.mulesoft.org/schema/mule/email"
	xmlns:http="http://www.mulesoft.org/schema/mule/http"
	xmlns:smb="http://www.mulesoft.org/schema/mule/smb"
	xmlns:tls="http://www.mulesoft.org/schema/mule/tls"
	xmlns:salesforce="http://www.mulesoft.org/schema/mule/salesforce"
	xmlns:secure-properties="http://www.mulesoft.org/schema/mule/secure-properties"
	xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="
http://www.mulesoft.org/schema/mule/email http://www.mulesoft.org/schema/mule/email/current/mule-email.xsd 
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd 
http://www.mulesoft.org/schema/mule/tls http://www.mulesoft.org/schema/mule/tls/current/mule-tls.xsd http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/secure-properties http://www.mulesoft.org/schema/mule/secure-properties/current/mule-secure-properties.xsd
http://www.mulesoft.org/schema/mule/salesforce http://www.mulesoft.org/schema/mule/salesforce/current/mule-salesforce.xsd
http://www.mulesoft.org/schema/mule/smb http://www.mulesoft.org/schema/mule/smb/current/mule-smb.xsd
http://www.mulesoft.org/schema/mule/xero-accounting http://www.mulesoft.org/schema/mule/xero-accounting/current/mule-xero-accounting.xsd
http://www.mulesoft.org/schema/mule/api-gateway http://www.mulesoft.org/schema/mule/api-gateway/current/mule-api-gateway.xsd
http://www.mulesoft.org/schema/mule/os http://www.mulesoft.org/schema/mule/os/current/mule-os.xsd
http://www.mulesoft.org/schema/mule/validation http://www.mulesoft.org/schema/mule/validation/current/mule-validation.xsd
http://www.mulesoft.org/schema/mule/vm http://www.mulesoft.org/schema/mule/vm/current/mule-vm.xsd
http://www.mulesoft.org/schema/mule/db http://www.mulesoft.org/schema/mule/db/current/mule-db.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd
http://www.mulesoft.org/schema/mule/oauth http://www.mulesoft.org/schema/mule/oauth/current/mule-oauth.xsd">

	<global-property doc:name="Global Property"
		doc:id="599a7252-a24a-4467-aaef-6d7fa42b96dd" name="env_local"
		value="false" doc:description="PROPERTIES" />

	<configuration-properties
		doc:name="config-common" doc:id="97562249-86b3-4568-9a01-403a5aadf8ac"
		file="properties/config-common.properties"
		doc:description="PROPERTIES" />
	<configuration-properties
		doc:name="config-env" doc:id="e1370c7a-94c8-4a37-bd5d-d31f363c387c"
		file="properties/config-${mule.env}.properties"
		doc:description="PROPERTIES" />

	<secure-properties:config
		name="config-env-secure" doc:name="Secure Properties Config"
		doc:id="966afefa-3b4f-409e-aff4-37e763ba0116"
		file="properties/config-${mule.env}-secure.properties"
		key="${mule.key}" encoding="UTF-8" doc:description="SECURE PROPERTIES">
		<secure-properties:encrypt
			algorithm="Blowfish" />
	</secure-properties:config>


	<configuration doc:name="global-error-handler"
		doc:id="db5e674d-29cb-4519-aa1d-b0a20bceda5c"
		defaultErrorHandler-ref="global-error-handler"
		doc:description="ERROR HANDLER" />

	<api-gateway:autodiscovery
		apiId="${secure::api.autodiscovery.id}" ignoreBasePath="true"
		doc:name="api_autodiscovery"
		doc:id="4bc94689-5e14-4395-a9d6-0f64176c6fc8" flowRef="main"
		doc:description="API INSTANCE ID" />


	<tls:context name="tls_context_inbound"
		doc:name="TLS Context" doc:id="6164b2fd-6227-43ce-b739-6cf4f341a736"
		doc:description="HTTPS">
		<tls:key-store type="jks"
			path="${secure::https.listener.keystore.path}"
			keyPassword="${secure::https.listener.keystore.keyPassword}"
			password="${secure::https.listener.keystore.password}" />			
	</tls:context>

	<http:listener-config name="https_listener"
		doc:name="HTTP Listener config"
		doc:id="5d840813-0583-4d7a-81eb-73a0dd318ca6"
		basePath="${https.listener.basePath}" doc:description="HTTPS">
		<http:listener-connection
			host="${https.listener.host}" port="${https.listener.port}"
			protocol="HTTPS" readTimeout="${https.listener.read.timeout}"
			connectionIdleTimeout="${https.listener.idle.timeout}"
			tlsContext="tls_context_inbound">
			<reconnection />

		</http:listener-connection>
	</http:listener-config>

	<http:request-config
		name="https_request_artemisapi" doc:name="HTTP Request configuration"
		doc:id="33e61113-ca0b-4e24-9929-abd4a9308529"
		basePath="${artemis.api.https.request.basePath}"
		doc:description="ARTEMIS API" responseTimeout="5000000">
		<http:request-connection
			host="${artemis.api.https.request.host}"
			port="${artemis.api.https.request.port}"
			usePersistentConnections="false"
			maxConnections="${artemis.api.https.request.connection.max}"
			connectionIdleTimeout="5000000" protocol="HTTPS">
			<reconnection>
				<reconnect
					frequency="${artemis.api.https.request.reconnection.frequency}"
					count="${artemis.api.https.request.reconnection.attempts}"
					blocking="false" />
			</reconnection>
			<tls:context >
				<tls:trust-store insecure="true" />
			</tls:context>

		</http:request-connection>

		<http:default-headers>
			<http:default-header key="ClientID"
				value="${secure::artemis.api.https.request.headers.clientId}" />
			<http:default-header key="ClientSecret"
				value="${secure::artemis.api.https.request.headers.clientSecret}" />

		</http:default-headers>
	</http:request-config>
	<validation:config name="validation_configuration"
		doc:name="Validation Config"
		doc:id="f30adbbe-eb1b-421b-bf24-441d3a35a21c"
		doc:description="VALIDATION" />
	<vm:config name="vm_configuration" doc:name="VM Config"
		doc:id="488636a3-d50c-49f4-8e30-2d0c4ed75b70"
		sendCorrelationId="ALWAYS" doc:description="VM">
		<vm:connection>
			<reconnection />
		</vm:connection>
		<vm:queues>
			<vm:queue queueName="documentMetadataInsertFailures" />
		</vm:queues>
	</vm:config>
	<os:config name="os_salesforce_auth_config"
		doc:name="ObjectStore Config"
		doc:id="ea269865-528b-42db-8813-e5b7416c1f46">
		<os:connection>
			<reconnection />
		</os:connection>
	</os:config>
	<os:config name="os_salesforce_leads_config"
		doc:name="ObjectStore Config"
		doc:id="6082db49-9b72-414a-9182-8d2c4164900a">
		<os:connection>
			<reconnection />
		</os:connection>
	</os:config>
	<os:object-store name="os_salesforce_auth"
		doc:name="Object store" doc:id="17ea6931-50f5-4f54-a8dc-c642c2ad8c33"
		config-ref="os_salesforce_auth_config" entryTtl="24"
		entryTtlUnit="HOURS" expirationInterval="24"
		expirationIntervalUnit="HOURS" />
	<os:object-store name="os_salesforce_leads"
		doc:name="Object store" doc:id="2b901868-41c6-4247-aec0-2638ecc32b73"
		config-ref="os_salesforce_leads_config" />





</mule>
